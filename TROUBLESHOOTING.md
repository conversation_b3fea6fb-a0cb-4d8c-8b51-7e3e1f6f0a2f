# دليل استكشاف الأخطاء - نظام شهادات المنشأ

## المشاكل الشائعة وحلولها

### 1. خطأ: "docker-compose is not recognized"

**السبب:** Docker Compose غير مثبت أو غير متوفر في PATH

**الحلول:**

#### الحل الأول: استخدام docker compose بدلاً من docker-compose
```powershell
# بدلاً من
docker-compose up

# استخدم
docker compose up
```

#### الحل الثاني: تثبيت Docker Compose منفصلاً
```powershell
# تحميل Docker Compose
$url = "https://github.com/docker/compose/releases/latest/download/docker-compose-windows-x86_64.exe"
$output = "$env:ProgramFiles\Docker\Docker\resources\bin\docker-compose.exe"
Invoke-WebRequest -Uri $url -OutFile $output
```

#### الحل الثالث: استخدام السكريبت المحدث
```powershell
# السكريبت المحدث يتعامل مع هذه المشكلة تلقائياً
.\start-docker.ps1
```

### 2. خطأ: "Access is denied" أو "Docker daemon is not running"

**السبب:** Docker غير مثبت أو غير مُشغل

**الحلول:**

#### تحقق من تثبيت Docker:
```powershell
# فحص المتطلبات
.\check-requirements.ps1

# تثبيت Docker إذا لم يكن مثبت
.\install-docker-simple.ps1
```

#### تشغيل Docker Desktop:
1. ابحث عن "Docker Desktop" في قائمة Start
2. شغل Docker Desktop
3. انتظر حتى يظهر أيقونة Docker في system tray
4. تأكد من أن الأيقونة خضراء (تعني أن Docker يعمل)

#### إعادة تشغيل خدمة Docker:
```powershell
# تشغيل PowerShell كمدير
Restart-Service docker
```

### 3. خطأ: "Port 8000 is already in use"

**السبب:** البورت 8000 مستخدم من تطبيق آخر

**الحلول:**

#### العثور على العملية التي تستخدم البورت:
```powershell
netstat -ano | findstr :8000
```

#### إيقاف العملية:
```powershell
# استبدل <PID> برقم العملية
taskkill /PID <PID> /F
```

#### استخدام بورت مختلف:
```powershell
# عدل docker-compose.yml
# غير "8000:8000" إلى "8080:8000"
```

### 4. خطأ: "No space left on device"

**السبب:** مساحة القرص الصلب ممتلئة

**الحلول:**

#### تنظيف Docker:
```powershell
# تنظيف الصور والحاويات غير المستخدمة
docker system prune -a -f

# تنظيف الـ volumes
docker volume prune -f
```

#### تنظيف Windows:
```powershell
# تشغيل Disk Cleanup
cleanmgr /sagerun:1
```

### 5. خطأ: "Build failed" أو "pip install failed"

**السبب:** مشاكل في الشبكة أو dependencies

**الحلول:**

#### إعادة البناء بدون cache:
```powershell
docker-compose build --no-cache
```

#### فحص الشبكة:
```powershell
# اختبار الاتصال بالإنترنت
Test-NetConnection google.com -Port 80
```

#### استخدام proxy إذا كان متوفر:
```dockerfile
# في Dockerfile، أضف قبل pip install
ENV HTTP_PROXY=http://your-proxy:port
ENV HTTPS_PROXY=http://your-proxy:port
```

### 6. خطأ: "Database connection failed"

**السبب:** مشاكل في قاعدة البيانات

**الحلول:**

#### فحص حالة قاعدة البيانات:
```powershell
docker-compose ps db
docker-compose logs db
```

#### إعادة تشغيل قاعدة البيانات:
```powershell
docker-compose restart db
```

#### إعادة إنشاء قاعدة البيانات:
```powershell
docker-compose down -v
docker-compose up -d db
# انتظر دقيقة ثم
docker-compose up -d web
```

### 7. خطأ: "Permission denied" في Linux containers

**السبب:** مشاكل في الأذونات

**الحلول:**

#### إعادة تعيين الأذونات:
```powershell
docker-compose exec web chown -R app:app /app
```

#### إعادة بناء الصورة:
```powershell
docker-compose build --no-cache web
```

### 8. خطأ: "Module not found" أو "Import Error"

**السبب:** مشاكل في Python dependencies

**الحلول:**

#### إعادة تثبيت المتطلبات:
```powershell
docker-compose exec web pip install -r requirements.txt
```

#### إعادة بناء الصورة:
```powershell
docker-compose build --no-cache web
```

### 9. الموقع لا يفتح على http://localhost:8000

**السبب:** مشاكل في الشبكة أو الإعدادات

**الحلول:**

#### فحص حالة الحاويات:
```powershell
docker-compose ps
```

#### فحص السجلات:
```powershell
docker-compose logs web
```

#### جرب عناوين أخرى:
- http://127.0.0.1:8000
- http://0.0.0.0:8000

#### فحص الـ firewall:
```powershell
# تعطيل Windows Firewall مؤقتاً للاختبار
netsh advfirewall set allprofiles state off
# لا تنس إعادة تفعيله بعد الاختبار
netsh advfirewall set allprofiles state on
```

### 10. خطأ: "Hyper-V is not available"

**السبب:** Hyper-V غير مفعل أو غير مدعوم

**الحلول:**

#### تفعيل Hyper-V:
```powershell
# تشغيل PowerShell كمدير
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All
```

#### استخدام WSL2 بدلاً من Hyper-V:
1. تثبيت WSL2
2. في Docker Desktop Settings
3. اختر "Use WSL2 based engine"

## أدوات التشخيص

### فحص شامل للنظام:
```powershell
.\check-requirements.ps1
```

### اختبار النظام:
```powershell
.\test-system.ps1
```

### عرض السجلات:
```powershell
# سجلات جميع الخدمات
docker-compose logs

# سجلات خدمة معينة
docker-compose logs web
docker-compose logs db

# متابعة السجلات المباشرة
docker-compose logs -f
```

### فحص استخدام الموارد:
```powershell
# استخدام الذاكرة والمعالج
docker stats

# مساحة القرص
docker system df
```

## طلب المساعدة

إذا لم تحل المشاكل أعلاه مشكلتك:

1. **اجمع المعلومات:**
   ```powershell
   .\check-requirements.ps1 > system-info.txt
   docker-compose logs > docker-logs.txt
   ```

2. **قدم المعلومات التالية:**
   - نسخة Windows
   - نسخة Docker
   - رسالة الخطأ الكاملة
   - الخطوات التي اتبعتها
   - محتوى ملف .env (بدون كلمات المرور)

3. **جرب الحلول الأساسية:**
   - إعادة تشغيل Docker Desktop
   - إعادة تشغيل الكمبيوتر
   - إعادة بناء الصور: `docker-compose build --no-cache`
