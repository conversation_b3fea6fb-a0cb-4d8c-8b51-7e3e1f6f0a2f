# سكريبت إنشاء المستخدمين لنظام شهادات المنشأ
# Script to create users for Origin Certificate System

Write-Host "🔐 إنشاء المستخدمين لنظام شهادات المنشأ" -ForegroundColor Green
Write-Host "Creating Users for Origin Certificate System" -ForegroundColor Gray
Write-Host "=" * 60 -ForegroundColor Cyan

# التحقق من تشغيل Docker
Write-Host "`nفحص حالة Docker..." -ForegroundColor Yellow
Write-Host "Checking Docker status..." -ForegroundColor Gray

try {
    docker ps | Out-Null
    Write-Host "✓ Docker يعمل" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker لا يعمل - يرجى تشغيل النظام أولاً" -ForegroundColor Red
    Write-Host "✗ Docker not running - please start the system first" -ForegroundColor Gray
    exit 1
}

# التحقق من وجود حاوية التطبيق
Write-Host "فحص حاوية التطبيق..." -ForegroundColor Yellow
Write-Host "Checking application container..." -ForegroundColor Gray

$containerName = ""
$containers = @("origin_web", "origin-certificate_web_1", "origin-certificate-web-1")

foreach ($container in $containers) {
    try {
        docker exec $container echo "test" 2>$null | Out-Null
        if ($LASTEXITCODE -eq 0) {
            $containerName = $container
            break
        }
    } catch {}
}

if ($containerName -eq "") {
    Write-Host "✗ لم يتم العثور على حاوية التطبيق" -ForegroundColor Red
    Write-Host "✗ Application container not found" -ForegroundColor Gray
    Write-Host "الحاويات المتاحة:" -ForegroundColor Yellow
    docker ps --format "{{.Names}}"
    exit 1
}

Write-Host "✓ تم العثور على الحاوية: $containerName" -ForegroundColor Green
Write-Host "✓ Found container: $containerName" -ForegroundColor Gray

# إنشاء ملف Python لإنشاء المستخدمين
Write-Host "`nإنشاء ملف إعداد المستخدمين..." -ForegroundColor Yellow
Write-Host "Creating user setup script..." -ForegroundColor Gray

$pythonScript = @"
import os
import django
from django.contrib.auth.models import User
from certificate_app.models import Branch, UserProfile

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OriginCertificateSystem.settings')
django.setup()

print("🚀 بدء إنشاء المستخدمين...")
print("🚀 Starting user creation...")

# إنشاء الفروع
branches_data = [
    "محطة الرمل",
    "السلطان حسين", 
    "الاستثماري"
]

branches = {}
for branch_name in branches_data:
    branch, created = Branch.objects.get_or_create(name=branch_name)
    branches[branch_name] = branch
    if created:
        print(f"✓ تم إنشاء الفرع: {branch_name}")
    else:
        print(f"✓ الفرع موجود: {branch_name}")

# إنشاء المدير العام
print("\n👑 إنشاء المدير العام...")
if not User.objects.filter(username='super_admin').exists():
    super_user = User.objects.create_superuser(
        username='super_admin',
        email='<EMAIL>',
        password='securepassword123',
        first_name='المدير',
        last_name='العام'
    )
    print("✓ تم إنشاء المدير العام: super_admin")
else:
    print("✓ المدير العام موجود: super_admin")

# إنشاء مديري الفروع
print("\n🏢 إنشاء مديري الفروع...")
branch_admins = [
    ("محطة_الرمل_admin", "محطة الرمل", "مدير", "محطة الرمل"),
    ("السلطان_حسين_admin", "السلطان حسين", "مدير", "السلطان حسين"),
    ("الاستثماري_admin", "الاستثماري", "مدير", "الاستثماري")
]

for username, branch_name, first_name, last_name in branch_admins:
    if not User.objects.filter(username=username).exists():
        user = User.objects.create_user(
            username=username,
            email=f"{username}@example.com",
            password='securepassword123',
            first_name=first_name,
            last_name=last_name,
            is_staff=True
        )
        
        # إنشاء ملف المستخدم
        UserProfile.objects.create(
            user=user,
            branch=branches[branch_name],
            is_branch_admin=True,
            is_branch_user=False
        )
        print(f"✓ تم إنشاء مدير الفرع: {username}")
    else:
        print(f"✓ مدير الفرع موجود: {username}")

# إنشاء مستخدمي الفروع
print("\n👥 إنشاء مستخدمي الفروع...")
branch_users = [
    ("محطة_الرمل_user", "محطة الرمل", "مستخدم", "محطة الرمل"),
    ("السلطان_حسين_user", "السلطان حسين", "مستخدم", "السلطان حسين"),
    ("الاستثماري_user", "الاستثماري", "مستخدم", "الاستثماري")
]

for username, branch_name, first_name, last_name in branch_users:
    if not User.objects.filter(username=username).exists():
        user = User.objects.create_user(
            username=username,
            email=f"{username}@example.com",
            password='securepassword123',
            first_name=first_name,
            last_name=last_name
        )
        
        # إنشاء ملف المستخدم
        UserProfile.objects.create(
            user=user,
            branch=branches[branch_name],
            is_branch_admin=False,
            is_branch_user=True
        )
        print(f"✓ تم إنشاء مستخدم الفرع: {username}")
    else:
        print(f"✓ مستخدم الفرع موجود: {username}")

print("\n🎉 تم الانتهاء من إنشاء جميع المستخدمين!")
print("🎉 All users created successfully!")

# عرض ملخص المستخدمين
print("\n📊 ملخص المستخدمين:")
print("📊 Users Summary:")
print("-" * 50)

all_users = User.objects.all().order_by('date_joined')
for user in all_users:
    try:
        profile = user.userprofile
        if profile.is_branch_admin:
            role = f"مدير فرع {profile.branch.name}"
        elif profile.is_branch_user:
            role = f"مستخدم فرع {profile.branch.name}"
        else:
            role = "غير محدد"
    except:
        if user.is_superuser:
            role = "المدير العام"
        else:
            role = "غير محدد"
    
    print(f"👤 {user.username} - {role}")

print("\n🔗 روابط الوصول:")
print("🔗 Access Links:")
print("التطبيق الرئيسي: http://localhost:8000")
print("لوحة الإدارة: http://localhost:8000/admin")
"@

# كتابة الملف المؤقت
$tempFile = "temp_create_users.py"
$pythonScript | Out-File -FilePath $tempFile -Encoding UTF8

# نسخ الملف إلى الحاوية وتشغيله
Write-Host "تشغيل سكريبت إنشاء المستخدمين..." -ForegroundColor Yellow
Write-Host "Running user creation script..." -ForegroundColor Gray

try {
    # نسخ الملف إلى الحاوية
    docker cp $tempFile "${containerName}:/app/create_users.py"
    
    # تشغيل السكريبت
    docker exec $containerName python create_users.py
    
    # حذف الملف المؤقت
    docker exec $containerName rm create_users.py
    Remove-Item $tempFile -Force
    
    Write-Host "`n🎉 تم إنشاء جميع المستخدمين بنجاح!" -ForegroundColor Green
    Write-Host "🎉 All users created successfully!" -ForegroundColor Gray
    
} catch {
    Write-Host "✗ حدث خطأ أثناء إنشاء المستخدمين" -ForegroundColor Red
    Write-Host "✗ Error occurred while creating users" -ForegroundColor Gray
    Write-Host $_.Exception.Message -ForegroundColor Red
}

# عرض معلومات تسجيل الدخول
Write-Host "`n" + "=" * 60 -ForegroundColor Cyan
Write-Host "🔐 معلومات تسجيل الدخول" -ForegroundColor Green
Write-Host "🔐 Login Information" -ForegroundColor Gray
Write-Host "=" * 60 -ForegroundColor Cyan

Write-Host "`n👑 المدير العام (Super Admin):" -ForegroundColor Yellow
Write-Host "اسم المستخدم: super_admin" -ForegroundColor White
Write-Host "كلمة المرور: securepassword123" -ForegroundColor White

Write-Host "`n🏢 مديري الفروع (Branch Admins):" -ForegroundColor Yellow
Write-Host "محطة الرمل: محطة_الرمل_admin / securepassword123" -ForegroundColor White
Write-Host "السلطان حسين: السلطان_حسين_admin / securepassword123" -ForegroundColor White
Write-Host "الاستثماري: الاستثماري_admin / securepassword123" -ForegroundColor White

Write-Host "`n👥 مستخدمي الفروع (Branch Users):" -ForegroundColor Yellow
Write-Host "محطة الرمل: محطة_الرمل_user / securepassword123" -ForegroundColor White
Write-Host "السلطان حسين: السلطان_حسين_user / securepassword123" -ForegroundColor White
Write-Host "الاستثماري: الاستثماري_user / securepassword123" -ForegroundColor White

Write-Host "`n🌐 روابط الوصول:" -ForegroundColor Cyan
Write-Host "التطبيق: http://localhost:8000" -ForegroundColor White
Write-Host "الإدارة: http://localhost:8000/admin" -ForegroundColor White

Write-Host "`n⚠️ تذكير أمني:" -ForegroundColor Red
Write-Host "يرجى تغيير كلمات المرور فور التشغيل الأول في بيئة الإنتاج" -ForegroundColor Yellow
Write-Host "Please change passwords immediately in production environment" -ForegroundColor Gray

Write-Host "`nاضغط Enter للمتابعة..." -ForegroundColor Gray
Read-Host
