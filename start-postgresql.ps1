# Start Origin Certificate System with PostgreSQL
Write-Host "Starting Origin Certificate System with PostgreSQL..." -ForegroundColor Green

# Check Docker
Write-Host "Checking Docker..." -ForegroundColor Yellow
try {
    docker version | Out-Null
    Write-Host "Docker is running" -ForegroundColor Green
} catch {
    Write-Host "Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
    exit 1
}

# Check .env file
if (!(Test-Path ".env")) {
    Write-Host "Creating .env file..." -ForegroundColor Yellow
    Copy-Item ".env.example" ".env"
}

# Stop existing containers
Write-Host "Stopping existing containers..." -ForegroundColor Yellow
docker stop origin_web origin_db 2>$null
docker rm origin_web origin_db 2>$null

# Create network
Write-Host "Creating Docker network..." -ForegroundColor Yellow
docker network create origin_network 2>$null

# Start PostgreSQL
Write-Host "Starting PostgreSQL..." -ForegroundColor Yellow
docker run -d --name origin_db --network origin_network -e POSTGRES_DB=origin_certificate -e POSTGRES_USER=django_user -e POSTGRES_PASSWORD=django_password_2024 -p 5432:5432 postgres:15

# Wait for PostgreSQL
Write-Host "Waiting for PostgreSQL to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# Build Django app
Write-Host "Building Django application..." -ForegroundColor Yellow
docker build -f Dockerfile.production -t origin-web .

# Start Django app
Write-Host "Starting Django application..." -ForegroundColor Yellow
docker run -d --name origin_web --network origin_network -e DATABASE_URL=************************************************************/origin_certificate -e ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0 -p 8000:8000 origin-web

# Wait for Django
Write-Host "Waiting for Django to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 20

# Run migrations
Write-Host "Running database migrations..." -ForegroundColor Yellow
docker exec origin_web python manage.py migrate

# Check status
Write-Host "Checking system status..." -ForegroundColor Yellow
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Test connection
Write-Host "Testing application..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000" -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "System is running successfully!" -ForegroundColor Green
    }
} catch {
    Write-Host "Application might need more time to start" -ForegroundColor Yellow
}

Write-Host "`nSystem Information:" -ForegroundColor Cyan
Write-Host "Application: http://localhost:8000" -ForegroundColor White
Write-Host "Admin Panel: http://localhost:8000/admin" -ForegroundColor White
Write-Host "Database: PostgreSQL on localhost:5432" -ForegroundColor White

Write-Host "`nUseful Commands:" -ForegroundColor Cyan
Write-Host "Create users: .\create-users.ps1" -ForegroundColor White
Write-Host "View logs: docker logs origin_web -f" -ForegroundColor White
Write-Host "Database shell: docker exec -it origin_db psql -U django_user -d origin_certificate" -ForegroundColor White
Write-Host "Stop system: docker stop origin_web origin_db" -ForegroundColor White

$createUsers = Read-Host "`nCreate default users now? (y/n)"
if ($createUsers -eq "y" -or $createUsers -eq "Y") {
    Write-Host "Creating users..." -ForegroundColor Yellow
    .\create-users.ps1
}

Write-Host "`nPress Enter to exit..." -ForegroundColor Gray
Read-Host
