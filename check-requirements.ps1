# Script to check system requirements for Origin Certificate System

Write-Host "=== System Requirements Check ===" -ForegroundColor Green

$allGood = $true

# Check Windows version
Write-Host "`n1. Checking Windows version..." -ForegroundColor Yellow
$osVersion = [System.Environment]::OSVersion.Version
$osName = (Get-WmiObject -Class Win32_OperatingSystem).Caption

Write-Host "OS: $osName" -ForegroundColor Cyan
Write-Host "Version: $($osVersion.Major).$($osVersion.Minor).$($osVersion.Build)" -ForegroundColor Cyan

if ($osVersion.Major -ge 10) {
    Write-Host "✓ Windows version is compatible" -ForegroundColor Green
} else {
    Write-Host "✗ Windows 10 or Server 2016+ required" -ForegroundColor Red
    $allGood = $false
}

# Check if running as Administrator
Write-Host "`n2. Checking Administrator privileges..." -ForegroundColor Yellow
if (([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "✓ Running as Administrator" -ForegroundColor Green
} else {
    Write-Host "⚠ Not running as Administrator (required for Docker installation)" -ForegroundColor Yellow
}

# Check available memory
Write-Host "`n3. Checking system memory..." -ForegroundColor Yellow
$memory = Get-WmiObject -Class Win32_ComputerSystem
$totalMemoryGB = [math]::Round($memory.TotalPhysicalMemory / 1GB, 2)

Write-Host "Total RAM: $totalMemoryGB GB" -ForegroundColor Cyan

if ($totalMemoryGB -ge 8) {
    Write-Host "✓ Sufficient memory available" -ForegroundColor Green
} elseif ($totalMemoryGB -ge 4) {
    Write-Host "⚠ 8GB+ recommended, but 4GB+ might work" -ForegroundColor Yellow
} else {
    Write-Host "✗ Insufficient memory (8GB+ recommended)" -ForegroundColor Red
    $allGood = $false
}

# Check available disk space
Write-Host "`n4. Checking disk space..." -ForegroundColor Yellow
$disk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
$freeSpaceGB = [math]::Round($disk.FreeSpace / 1GB, 2)

Write-Host "Free space on C: $freeSpaceGB GB" -ForegroundColor Cyan

if ($freeSpaceGB -ge 50) {
    Write-Host "✓ Sufficient disk space available" -ForegroundColor Green
} elseif ($freeSpaceGB -ge 20) {
    Write-Host "⚠ 50GB+ recommended, but might work with $freeSpaceGB GB" -ForegroundColor Yellow
} else {
    Write-Host "✗ Insufficient disk space (50GB+ recommended)" -ForegroundColor Red
    $allGood = $false
}

# Check Hyper-V capability
Write-Host "`n5. Checking Hyper-V support..." -ForegroundColor Yellow
try {
    $hyperv = Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-All
    if ($hyperv.State -eq "Enabled") {
        Write-Host "✓ Hyper-V is enabled" -ForegroundColor Green
    } else {
        Write-Host "⚠ Hyper-V is not enabled (will be enabled during Docker installation)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠ Could not check Hyper-V status" -ForegroundColor Yellow
}

# Check if Docker is installed
Write-Host "`n6. Checking Docker installation..." -ForegroundColor Yellow
if (Get-Command docker -ErrorAction SilentlyContinue) {
    try {
        $dockerVersion = docker --version
        Write-Host "✓ Docker is installed: $dockerVersion" -ForegroundColor Green
        
        # Check if Docker is running
        try {
            docker version | Out-Null
            Write-Host "✓ Docker is running" -ForegroundColor Green
        } catch {
            Write-Host "⚠ Docker is installed but not running" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠ Docker is installed but not accessible" -ForegroundColor Yellow
    }
} else {
    Write-Host "✗ Docker is not installed" -ForegroundColor Red
    Write-Host "  Run: .\install-docker-simple.ps1" -ForegroundColor Cyan
}

# Check if docker-compose is available
Write-Host "`n7. Checking Docker Compose..." -ForegroundColor Yellow
if (Get-Command docker-compose -ErrorAction SilentlyContinue) {
    $composeVersion = docker-compose --version
    Write-Host "✓ docker-compose is available: $composeVersion" -ForegroundColor Green
} elseif (Get-Command docker -ErrorAction SilentlyContinue) {
    try {
        $composeVersion = docker compose version
        Write-Host "✓ docker compose is available: $composeVersion" -ForegroundColor Green
    } catch {
        Write-Host "⚠ Docker Compose not available" -ForegroundColor Yellow
    }
} else {
    Write-Host "✗ Docker Compose not available" -ForegroundColor Red
}

# Summary
Write-Host "`n=== Summary ===" -ForegroundColor Cyan
if ($allGood) {
    Write-Host "🎉 System meets all requirements!" -ForegroundColor Green
    Write-Host "You can proceed with the installation." -ForegroundColor White
} else {
    Write-Host "⚠ Some requirements are not met." -ForegroundColor Yellow
    Write-Host "Please address the issues above before proceeding." -ForegroundColor White
}

Write-Host "`n=== Next Steps ===" -ForegroundColor Cyan
if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Host "1. Install Docker: .\install-docker-simple.ps1" -ForegroundColor White
    Write-Host "2. Restart computer" -ForegroundColor White
    Write-Host "3. Start Docker Desktop" -ForegroundColor White
    Write-Host "4. Run: .\start-docker.ps1" -ForegroundColor White
} else {
    Write-Host "1. Make sure Docker Desktop is running" -ForegroundColor White
    Write-Host "2. Run: .\start-docker.ps1" -ForegroundColor White
}
