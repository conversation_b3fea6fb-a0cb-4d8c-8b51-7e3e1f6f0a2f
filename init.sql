-- Initialize the database for Origin Certificate System
-- This script runs when the PostgreSQL container starts for the first time

-- Create the database if it doesn't exist
-- (This is handled by POSTGRES_DB environment variable)

-- Grant all privileges to the django user
GRANT ALL PRIVILEGES ON DATABASE origin_certificate TO django_user;

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Set timezone
SET timezone = 'UTC';
