# 🐘 دليل PostgreSQL - نظام شهادات المنشأ

## 🎯 نظرة عامة

تم تحديث نظام شهادات المنشأ لاستخدام **PostgreSQL** كقاعدة بيانات رئيسية بدلاً من SQLite، مما يوفر:

- ✅ **أداء أفضل** للبيانات الكبيرة
- ✅ **دعم متقدم للاستعلامات**
- ✅ **أمان محسن**
- ✅ **دعم المعاملات المتقدمة**
- ✅ **قابلية التوسع**

---

## 🚀 طرق التشغيل

### 1️⃣ التشغيل السريع مع PostgreSQL
```powershell
.\start-with-postgresql.ps1
```

### 2️⃣ التشغيل باستخدام Docker Compose
```powershell
.\start-docker.ps1
```
*(الآن يستخدم PostgreSQL افتراضياً)*

### 3️⃣ التشغيل الشامل
```powershell
.\RUN-SYSTEM.ps1
```

---

## 🔧 إدارة PostgreSQL

### إدارة قاعدة البيانات:
```powershell
# بدء PostgreSQL
.\postgresql-manager.ps1 -Action start

# فحص الحالة
.\postgresql-manager.ps1 -Action status

# إيقاف PostgreSQL
.\postgresql-manager.ps1 -Action stop

# إعادة تشغيل
.\postgresql-manager.ps1 -Action restart
```

### النسخ الاحتياطي والاستعادة:
```powershell
# إنشاء نسخة احتياطية
.\postgresql-manager.ps1 -Action backup

# استعادة نسخة احتياطية
.\postgresql-manager.ps1 -Action restore -BackupFile backup_20240101_120000.sql

# إعادة تعيين قاعدة البيانات
.\postgresql-manager.ps1 -Action reset
```

### الوصول لقاعدة البيانات:
```powershell
# فتح PostgreSQL shell
.\postgresql-manager.ps1 -Action shell

# عرض السجلات
.\postgresql-manager.ps1 -Action logs
```

---

## 📊 معلومات الاتصال

| المعلومة | القيمة |
|---------|--------|
| **المضيف** | `localhost` |
| **البورت** | `5432` |
| **قاعدة البيانات** | `origin_certificate` |
| **المستخدم** | `django_user` |
| **كلمة المرور** | `django_password_2024` |

### سلسلة الاتصال:
```
postgresql://django_user:django_password_2024@localhost:5432/origin_certificate
```

---

## 🛠️ أوامر PostgreSQL المفيدة

### من داخل PostgreSQL shell:
```sql
-- عرض جميع الجداول
\dt

-- عرض معلومات جدول معين
\d certificate_app_certificate

-- عرض جميع المستخدمين
SELECT username, email, is_staff, is_superuser FROM auth_user;

-- عرض الفروع
SELECT * FROM certificate_app_branch;

-- عرض الشهادات
SELECT id, certificatenumber, company_id, branch_id FROM certificate_app_certificate LIMIT 10;

-- إحصائيات سريعة
SELECT 
    (SELECT COUNT(*) FROM auth_user) as total_users,
    (SELECT COUNT(*) FROM certificate_app_certificate) as total_certificates,
    (SELECT COUNT(*) FROM certificate_app_branch) as total_branches;

-- الخروج
\q
```

### من سطر الأوامر:
```powershell
# الاتصال بقاعدة البيانات
docker exec -it origin_db psql -U django_user -d origin_certificate

# تشغيل استعلام مباشر
docker exec origin_db psql -U django_user -d origin_certificate -c "SELECT COUNT(*) FROM auth_user;"

# تصدير البيانات
docker exec origin_db pg_dump -U django_user origin_certificate > backup.sql

# استيراد البيانات
docker exec -i origin_db psql -U django_user origin_certificate < backup.sql
```

---

## 🔄 الترحيل من SQLite إلى PostgreSQL

إذا كان لديك بيانات في SQLite وتريد نقلها إلى PostgreSQL:

### 1. تصدير البيانات من SQLite:
```powershell
# تشغيل النظام القديم مع SQLite
docker exec origin_web python manage.py dumpdata > data_backup.json
```

### 2. تشغيل النظام الجديد مع PostgreSQL:
```powershell
.\start-with-postgresql.ps1
```

### 3. استيراد البيانات:
```powershell
# نسخ ملف البيانات إلى الحاوية
docker cp data_backup.json origin_web:/app/

# استيراد البيانات
docker exec origin_web python manage.py loaddata data_backup.json
```

---

## 📈 مراقبة الأداء

### فحص حالة الاتصالات:
```sql
-- عرض الاتصالات النشطة
SELECT pid, usename, application_name, client_addr, state 
FROM pg_stat_activity 
WHERE datname = 'origin_certificate';

-- عرض حجم قاعدة البيانات
SELECT pg_size_pretty(pg_database_size('origin_certificate'));

-- عرض أحجام الجداول
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### مراقبة الأداء:
```powershell
# عرض استخدام الموارد
docker stats origin_db

# عرض سجلات الأداء
docker logs origin_db | grep -i slow
```

---

## 🔒 الأمان

### تغيير كلمة مرور قاعدة البيانات:
```sql
-- من داخل PostgreSQL shell
ALTER USER django_user PASSWORD 'new_secure_password';
```

### إنشاء مستخدم جديد:
```sql
-- إنشاء مستخدم للقراءة فقط
CREATE USER readonly_user WITH PASSWORD 'readonly_password';
GRANT CONNECT ON DATABASE origin_certificate TO readonly_user;
GRANT USAGE ON SCHEMA public TO readonly_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO readonly_user;
```

---

## 🚨 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### ❌ خطأ: "connection refused"
```powershell
# فحص حالة PostgreSQL
.\postgresql-manager.ps1 -Action status

# إعادة تشغيل PostgreSQL
.\postgresql-manager.ps1 -Action restart
```

#### ❌ خطأ: "database does not exist"
```powershell
# إعادة إنشاء قاعدة البيانات
.\postgresql-manager.ps1 -Action reset
.\postgresql-manager.ps1 -Action start
```

#### ❌ خطأ: "authentication failed"
```powershell
# التحقق من متغيرات البيئة في .env
Get-Content .env | Select-String "DB_"
```

#### ❌ خطأ: "too many connections"
```sql
-- فحص الاتصالات النشطة
SELECT COUNT(*) FROM pg_stat_activity WHERE datname = 'origin_certificate';

-- إنهاء الاتصالات المعلقة
SELECT pg_terminate_backend(pid) 
FROM pg_stat_activity 
WHERE datname = 'origin_certificate' AND state = 'idle';
```

---

## 📚 مراجع إضافية

- **PostgreSQL الرسمي:** https://www.postgresql.org/docs/
- **Django مع PostgreSQL:** https://docs.djangoproject.com/en/stable/ref/databases/#postgresql-notes
- **أفضل الممارسات:** https://wiki.postgresql.org/wiki/Performance_Optimization

---

## ✅ قائمة التحقق

- [ ] تم تشغيل PostgreSQL بنجاح
- [ ] يمكن الاتصال بقاعدة البيانات
- [ ] تم تشغيل الترحيلات
- [ ] تم إنشاء المستخدمين
- [ ] يمكن الوصول للتطبيق
- [ ] تم إنشاء نسخة احتياطية

**🎉 إذا تمت كل هذه الخطوات، فالنظام يعمل بنجاح مع PostgreSQL!**
