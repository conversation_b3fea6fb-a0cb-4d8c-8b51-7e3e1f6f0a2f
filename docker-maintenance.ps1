# PowerShell script for Docker maintenance tasks
# For Origin Certificate System

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("backup", "restore", "logs", "shell", "migrate", "collectstatic", "createsuperuser", "restart", "stop", "clean")]
    [string]$Action,
    
    [string]$BackupFile = "backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').sql"
)

Write-Host "=== Origin Certificate System Maintenance ===" -ForegroundColor Green

switch ($Action) {
    "backup" {
        Write-Host "Creating database backup..." -ForegroundColor Yellow
        docker-compose exec db pg_dump -U django_user origin_certificate > $BackupFile
        Write-Host "Backup created: $BackupFile" -ForegroundColor Green
    }
    
    "restore" {
        if (!(Test-Path $BackupFile)) {
            Write-Host "Error: Backup file $BackupFile not found" -ForegroundColor Red
            exit 1
        }
        Write-Host "Restoring database from $BackupFile..." -ForegroundColor Yellow
        Get-Content $BackupFile | docker-compose exec -T db psql -U django_user origin_certificate
        Write-Host "Database restored successfully" -ForegroundColor Green
    }
    
    "logs" {
        Write-Host "Showing application logs..." -ForegroundColor Yellow
        docker-compose logs -f web
    }
    
    "shell" {
        Write-Host "Opening Django shell..." -ForegroundColor Yellow
        docker-compose exec web python manage.py shell
    }
    
    "migrate" {
        Write-Host "Running database migrations..." -ForegroundColor Yellow
        docker-compose exec web python manage.py migrate
        Write-Host "Migrations completed" -ForegroundColor Green
    }
    
    "collectstatic" {
        Write-Host "Collecting static files..." -ForegroundColor Yellow
        docker-compose exec web python manage.py collectstatic --noinput
        Write-Host "Static files collected" -ForegroundColor Green
    }
    
    "createsuperuser" {
        Write-Host "Creating Django superuser..." -ForegroundColor Yellow
        docker-compose exec web python manage.py createsuperuser
    }
    
    "restart" {
        Write-Host "Restarting services..." -ForegroundColor Yellow
        docker-compose restart
        Write-Host "Services restarted" -ForegroundColor Green
    }
    
    "stop" {
        Write-Host "Stopping all services..." -ForegroundColor Yellow
        docker-compose down
        Write-Host "All services stopped" -ForegroundColor Green
    }
    
    "clean" {
        Write-Host "Cleaning up Docker resources..." -ForegroundColor Yellow
        docker-compose down -v
        docker system prune -f
        Write-Host "Cleanup completed" -ForegroundColor Green
    }
}

Write-Host "=== Task Completed ===" -ForegroundColor Green
