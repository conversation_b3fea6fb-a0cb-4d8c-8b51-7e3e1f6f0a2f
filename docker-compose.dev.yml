version: '3.8'

services:
  # Django Application for Development
  web:
    build: 
      context: .
      dockerfile: Dockerfile
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - DEBUG=True
      - DJANGO_SETTINGS_MODULE=OriginCertificateSystem.settings
    volumes:
      - .:/app
      - static_volume:/app/static
      - media_volume:/app/media
    networks:
      - origin_network
    command: python manage.py runserver 0.0.0.0:8000

  # PostgreSQL Database (Optional for development)
  db:
    image: postgres:15
    restart: unless-stopped
    environment:
      POSTGRES_DB: origin_certificate
      POSTGRES_USER: django_user
      POSTGRES_PASSWORD: django_password_2024
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - origin_network

volumes:
  postgres_data_dev:
  static_volume:
  media_volume:

networks:
  origin_network:
    driver: bridge
