# مدير PostgreSQL لنظام شهادات المنشأ
# PostgreSQL Manager for Origin Certificate System

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("start", "stop", "restart", "status", "backup", "restore", "shell", "logs", "reset")]
    [string]$Action,
    
    [string]$BackupFile = "backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').sql"
)

Write-Host "🐘 مدير PostgreSQL - نظام شهادات المنشأ" -ForegroundColor Green
Write-Host "🐘 PostgreSQL Manager - Origin Certificate System" -ForegroundColor Gray
Write-Host "=" * 60 -ForegroundColor Cyan

# معلومات الاتصال
$DB_NAME = "origin_certificate"
$DB_USER = "django_user"
$DB_PASSWORD = "django_password_2024"
$CONTAINER_NAME = "origin_db"

switch ($Action) {
    "start" {
        Write-Host "`n🚀 بدء PostgreSQL..." -ForegroundColor Yellow
        Write-Host "Starting PostgreSQL..." -ForegroundColor Gray
        
        # إنشاء الشبكة إذا لم تكن موجودة
        docker network create origin_network 2>$null
        
        # تشغيل PostgreSQL
        docker run -d `
            --name $CONTAINER_NAME `
            --network origin_network `
            -e POSTGRES_DB=$DB_NAME `
            -e POSTGRES_USER=$DB_USER `
            -e POSTGRES_PASSWORD=$DB_PASSWORD `
            -p 5432:5432 `
            -v postgres_data:/var/lib/postgresql/data `
            postgres:15
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ PostgreSQL بدأ بنجاح" -ForegroundColor Green
            
            # انتظار الجاهزية
            Write-Host "انتظار الجاهزية..." -ForegroundColor Yellow
            Start-Sleep -Seconds 10
            
            # فحص الحالة
            docker exec $CONTAINER_NAME pg_isready -U $DB_USER
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ PostgreSQL جاهز للاستخدام" -ForegroundColor Green
            }
        } else {
            Write-Host "⚠ PostgreSQL قد يكون يعمل بالفعل" -ForegroundColor Yellow
        }
    }
    
    "stop" {
        Write-Host "`n🛑 إيقاف PostgreSQL..." -ForegroundColor Yellow
        Write-Host "Stopping PostgreSQL..." -ForegroundColor Gray
        
        docker stop $CONTAINER_NAME
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ تم إيقاف PostgreSQL" -ForegroundColor Green
        } else {
            Write-Host "⚠ PostgreSQL قد يكون متوقف بالفعل" -ForegroundColor Yellow
        }
    }
    
    "restart" {
        Write-Host "`n🔄 إعادة تشغيل PostgreSQL..." -ForegroundColor Yellow
        Write-Host "Restarting PostgreSQL..." -ForegroundColor Gray
        
        docker restart $CONTAINER_NAME
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ تم إعادة تشغيل PostgreSQL" -ForegroundColor Green
            
            # انتظار الجاهزية
            Start-Sleep -Seconds 5
            docker exec $CONTAINER_NAME pg_isready -U $DB_USER
        } else {
            Write-Host "✗ فشل في إعادة التشغيل" -ForegroundColor Red
        }
    }
    
    "status" {
        Write-Host "`n📊 حالة PostgreSQL..." -ForegroundColor Yellow
        Write-Host "PostgreSQL Status..." -ForegroundColor Gray
        
        # حالة الحاوية
        $containerStatus = docker ps -f name=$CONTAINER_NAME --format "{{.Status}}"
        if ($containerStatus) {
            Write-Host "✓ الحاوية: $containerStatus" -ForegroundColor Green
            
            # فحص الاتصال
            docker exec $CONTAINER_NAME pg_isready -U $DB_USER
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ قاعدة البيانات: متاحة" -ForegroundColor Green
            } else {
                Write-Host "⚠ قاعدة البيانات: غير متاحة" -ForegroundColor Yellow
            }
            
            # معلومات إضافية
            Write-Host "`nمعلومات الاتصال:" -ForegroundColor Cyan
            Write-Host "المضيف: localhost:5432" -ForegroundColor White
            Write-Host "قاعدة البيانات: $DB_NAME" -ForegroundColor White
            Write-Host "المستخدم: $DB_USER" -ForegroundColor White
        } else {
            Write-Host "✗ PostgreSQL غير يعمل" -ForegroundColor Red
        }
    }
    
    "backup" {
        Write-Host "`n💾 إنشاء نسخة احتياطية..." -ForegroundColor Yellow
        Write-Host "Creating backup..." -ForegroundColor Gray
        
        # فحص أن PostgreSQL يعمل
        docker exec $CONTAINER_NAME pg_isready -U $DB_USER | Out-Null
        if ($LASTEXITCODE -ne 0) {
            Write-Host "✗ PostgreSQL غير متاح" -ForegroundColor Red
            exit 1
        }
        
        # إنشاء النسخة الاحتياطية
        docker exec $CONTAINER_NAME pg_dump -U $DB_USER $DB_NAME > $BackupFile
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ تم إنشاء النسخة الاحتياطية: $BackupFile" -ForegroundColor Green
            $fileSize = (Get-Item $BackupFile).Length / 1KB
            Write-Host "حجم الملف: $([math]::Round($fileSize, 2)) KB" -ForegroundColor Cyan
        } else {
            Write-Host "✗ فشل في إنشاء النسخة الاحتياطية" -ForegroundColor Red
        }
    }
    
    "restore" {
        if (!(Test-Path $BackupFile)) {
            Write-Host "✗ ملف النسخة الاحتياطية غير موجود: $BackupFile" -ForegroundColor Red
            exit 1
        }
        
        Write-Host "`n📥 استعادة النسخة الاحتياطية..." -ForegroundColor Yellow
        Write-Host "Restoring backup..." -ForegroundColor Gray
        
        # تأكيد الاستعادة
        $confirm = Read-Host "هل أنت متأكد من استعادة $BackupFile؟ (y/n)"
        if ($confirm -ne "y" -and $confirm -ne "Y") {
            Write-Host "تم إلغاء الاستعادة" -ForegroundColor Yellow
            exit 0
        }
        
        # استعادة النسخة الاحتياطية
        Get-Content $BackupFile | docker exec -i $CONTAINER_NAME psql -U $DB_USER $DB_NAME
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ تم استعادة النسخة الاحتياطية بنجاح" -ForegroundColor Green
        } else {
            Write-Host "✗ فشل في استعادة النسخة الاحتياطية" -ForegroundColor Red
        }
    }
    
    "shell" {
        Write-Host "`n🐚 فتح shell PostgreSQL..." -ForegroundColor Yellow
        Write-Host "Opening PostgreSQL shell..." -ForegroundColor Gray
        
        Write-Host "للخروج اكتب: \q" -ForegroundColor Cyan
        Write-Host "To exit type: \q" -ForegroundColor Gray
        
        docker exec -it $CONTAINER_NAME psql -U $DB_USER $DB_NAME
    }
    
    "logs" {
        Write-Host "`n📋 سجلات PostgreSQL..." -ForegroundColor Yellow
        Write-Host "PostgreSQL Logs..." -ForegroundColor Gray
        
        docker logs $CONTAINER_NAME -f
    }
    
    "reset" {
        Write-Host "`n🔄 إعادة تعيين PostgreSQL..." -ForegroundColor Yellow
        Write-Host "Resetting PostgreSQL..." -ForegroundColor Gray
        
        $confirm = Read-Host "هل أنت متأكد من حذف جميع البيانات؟ (y/n)"
        if ($confirm -ne "y" -and $confirm -ne "Y") {
            Write-Host "تم إلغاء العملية" -ForegroundColor Yellow
            exit 0
        }
        
        # إيقاف وحذف الحاوية
        docker stop $CONTAINER_NAME 2>$null
        docker rm $CONTAINER_NAME 2>$null
        
        # حذف البيانات
        docker volume rm postgres_data 2>$null
        
        Write-Host "✓ تم إعادة تعيين PostgreSQL" -ForegroundColor Green
        Write-Host "يمكنك الآن تشغيل: .\postgresql-manager.ps1 -Action start" -ForegroundColor Cyan
    }
}

Write-Host "`n" + "=" * 60 -ForegroundColor Cyan
Write-Host "🛠️ أوامر مفيدة:" -ForegroundColor Cyan
Write-Host "البدء: .\postgresql-manager.ps1 -Action start" -ForegroundColor White
Write-Host "الحالة: .\postgresql-manager.ps1 -Action status" -ForegroundColor White
Write-Host "نسخة احتياطية: .\postgresql-manager.ps1 -Action backup" -ForegroundColor White
Write-Host "Shell: .\postgresql-manager.ps1 -Action shell" -ForegroundColor White
Write-Host "الإيقاف: .\postgresql-manager.ps1 -Action stop" -ForegroundColor White
