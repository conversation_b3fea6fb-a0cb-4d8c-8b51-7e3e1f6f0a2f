# تشغيل نظام شهادات المنشأ مع PostgreSQL
# Start Origin Certificate System with PostgreSQL

Write-Host "🐘 تشغيل نظام شهادات المنشأ مع PostgreSQL" -ForegroundColor Green
Write-Host "🐘 Starting Origin Certificate System with PostgreSQL" -ForegroundColor Gray
Write-Host "=" * 60 -ForegroundColor Cyan

# التحقق من Docker
Write-Host "`n📋 فحص Docker..." -ForegroundColor Yellow
if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Host "✗ Docker غير مثبت" -ForegroundColor Red
    Write-Host "يرجى تثبيت Docker أولاً: .\install-docker-simple.ps1" -ForegroundColor Yellow
    exit 1
}

try {
    docker version | Out-Null 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "Docker daemon not running"
    }
    Write-Host "✓ Docker يعمل" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker daemon لا يعمل" -ForegroundColor Red
    Write-Host "يرجى تشغيل Docker Desktop أولاً" -ForegroundColor Yellow
    exit 1
}

# التحقق من ملف .env
Write-Host "`n📋 فحص ملف الإعدادات..." -ForegroundColor Yellow
if (!(Test-Path ".env")) {
    Write-Host "⚠ ملف .env غير موجود - سيتم إنشاؤه" -ForegroundColor Yellow
    Copy-Item ".env.example" ".env"
    Write-Host "✓ تم إنشاء ملف .env" -ForegroundColor Green
}

# تحديد نوع Docker Compose
$dockerComposeCmd = ""
if (Get-Command docker-compose -ErrorAction SilentlyContinue) {
    $dockerComposeCmd = "docker-compose"
} else {
    try {
        docker compose version | Out-Null 2>&1
        if ($LASTEXITCODE -eq 0) {
            $dockerComposeCmd = "docker compose"
        }
    } catch {}
}

if ($dockerComposeCmd -eq "") {
    Write-Host "⚠ Docker Compose غير متوفر - سيتم استخدام Docker مباشرة" -ForegroundColor Yellow
    $useDockerCompose = $false
} else {
    Write-Host "✓ استخدام: $dockerComposeCmd" -ForegroundColor Green
    $useDockerCompose = $true
}

# إيقاف أي حاويات موجودة
Write-Host "`n🛑 إيقاف الحاويات الموجودة..." -ForegroundColor Yellow
if ($useDockerCompose) {
    Invoke-Expression "$dockerComposeCmd down" 2>$null
} else {
    docker stop origin_web origin_db 2>$null
    docker rm origin_web origin_db 2>$null
}

# إنشاء الشبكة
Write-Host "`n🌐 إنشاء شبكة Docker..." -ForegroundColor Yellow
docker network create origin_network 2>$null
Write-Host "✓ الشبكة جاهزة" -ForegroundColor Green

# تشغيل PostgreSQL
Write-Host "`n🐘 تشغيل قاعدة بيانات PostgreSQL..." -ForegroundColor Yellow
if ($useDockerCompose) {
    # استخدام docker-compose
    Invoke-Expression "$dockerComposeCmd up -d db"
} else {
    # استخدام Docker مباشرة
    docker run -d `
        --name origin_db `
        --network origin_network `
        -e POSTGRES_DB=origin_certificate `
        -e POSTGRES_USER=django_user `
        -e POSTGRES_PASSWORD=django_password_2024 `
        -p 5432:5432 `
        -v postgres_data:/var/lib/postgresql/data `
        postgres:15
}

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ PostgreSQL بدأ بنجاح" -ForegroundColor Green
} else {
    Write-Host "⚠ PostgreSQL قد يكون يعمل بالفعل" -ForegroundColor Yellow
}

# انتظار PostgreSQL
Write-Host "`n⏳ انتظار جاهزية PostgreSQL..." -ForegroundColor Yellow
$maxWait = 30
$waited = 0

while ($waited -lt $maxWait) {
    try {
        $result = docker exec origin_db pg_isready -U django_user 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ PostgreSQL جاهز!" -ForegroundColor Green
            break
        }
    } catch {}
    
    Start-Sleep -Seconds 2
    $waited += 2
    Write-Host "." -NoNewline -ForegroundColor Gray
}
Write-Host ""

if ($waited -ge $maxWait) {
    Write-Host "⚠ PostgreSQL يحتاج وقت إضافي للبدء" -ForegroundColor Yellow
}

# بناء تطبيق Django
Write-Host "`n🏗️ بناء تطبيق Django..." -ForegroundColor Yellow
docker build -f Dockerfile.production -t origin-web .

if ($LASTEXITCODE -ne 0) {
    Write-Host "✗ فشل في بناء التطبيق" -ForegroundColor Red
    exit 1
}
Write-Host "✓ تم بناء التطبيق بنجاح" -ForegroundColor Green

# تشغيل تطبيق Django
Write-Host "`n🚀 تشغيل تطبيق Django..." -ForegroundColor Yellow
if ($useDockerCompose) {
    # استخدام docker-compose
    Invoke-Expression "$dockerComposeCmd up -d web"
} else {
    # استخدام Docker مباشرة
    docker run -d `
        --name origin_web `
        --network origin_network `
        -e DATABASE_URL=************************************************************/origin_certificate `
        -e DEBUG=False `
        -e ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0 `
        -p 8000:8000 `
        origin-web
}

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Django بدأ بنجاح" -ForegroundColor Green
} else {
    Write-Host "✗ فشل في تشغيل Django" -ForegroundColor Red
    exit 1
}

# انتظار Django
Write-Host "`n⏳ انتظار جاهزية Django..." -ForegroundColor Yellow
Start-Sleep -Seconds 20

# تشغيل الترحيلات
Write-Host "`n📊 تشغيل ترحيلات قاعدة البيانات..." -ForegroundColor Yellow
docker exec origin_web python manage.py migrate

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ تم تشغيل الترحيلات بنجاح" -ForegroundColor Green
} else {
    Write-Host "⚠ قد تحتاج لتشغيل الترحيلات يدوياً" -ForegroundColor Yellow
}

# جمع الملفات الثابتة
Write-Host "`n📁 جمع الملفات الثابتة..." -ForegroundColor Yellow
docker exec origin_web python manage.py collectstatic --noinput 2>$null

# فحص حالة النظام
Write-Host "`n🔍 فحص حالة النظام..." -ForegroundColor Yellow
Write-Host "حالة الحاويات:" -ForegroundColor Cyan
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# اختبار الاتصال
Write-Host "`n🌐 اختبار الاتصال..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000" -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "🎉 النظام يعمل بنجاح مع PostgreSQL!" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ النظام قد يحتاج وقت إضافي للبدء" -ForegroundColor Yellow
}

# النتيجة النهائية
Write-Host "`n" + "=" * 60 -ForegroundColor Cyan
Write-Host "🎯 النظام جاهز مع PostgreSQL!" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Cyan

Write-Host "`n🌐 روابط الوصول:" -ForegroundColor Cyan
Write-Host "التطبيق الرئيسي: http://localhost:8000" -ForegroundColor White
Write-Host "لوحة الإدارة: http://localhost:8000/admin" -ForegroundColor White

Write-Host "`n🐘 معلومات PostgreSQL:" -ForegroundColor Cyan
Write-Host "المضيف: localhost:5432" -ForegroundColor White
Write-Host "قاعدة البيانات: origin_certificate" -ForegroundColor White
Write-Host "المستخدم: django_user" -ForegroundColor White

Write-Host "`n🛠️ أوامر مفيدة:" -ForegroundColor Cyan
Write-Host "إنشاء المستخدمين: .\create-users.ps1" -ForegroundColor White
Write-Host "عرض السجلات: docker logs origin_web -f" -ForegroundColor White
Write-Host "الدخول لقاعدة البيانات: docker exec -it origin_db psql -U django_user -d origin_certificate" -ForegroundColor White
Write-Host "إيقاف النظام: docker stop origin_web origin_db" -ForegroundColor White

$createUsers = Read-Host "`nهل تريد إنشاء المستخدمين الآن؟ (y/n)"
if ($createUsers -eq "y" -or $createUsers -eq "Y") {
    Write-Host "جاري إنشاء المستخدمين..." -ForegroundColor Yellow
    .\create-users.ps1
}

Write-Host "`nاضغط Enter للخروج..." -ForegroundColor Gray
Read-Host
