# Generated by Django 5.1.5 on 2025-05-27 11:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('certificate_app', '0016_remove_company_importcompanyaddress_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='certificate',
            name='importCompanyAddress',
        ),
        migrations.RemoveField(
            model_name='certificate',
            name='importCompanyName',
        ),
        migrations.RemoveField(
            model_name='certificate',
            name='importCompanyPhone',
        ),
        migrations.AddField(
            model_name='company',
            name='importCompanyAddress',
            field=models.Char<PERSON>ield(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='importCompanyName',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='importCompanyPhone',
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True),
        ),
    ]
