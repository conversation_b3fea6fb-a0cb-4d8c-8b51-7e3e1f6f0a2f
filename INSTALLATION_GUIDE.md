# دليل التثبيت الشامل - نظام شهادات المنشأ

## المتطلبات الأساسية

### نظام التشغيل:
- Windows Server 2019 أو أحدث
- Windows 10 Pro/Enterprise (للتطوير)

### المتطلبات التقنية:
- ذاكرة وصول عشوائي: 8 جيجابايت على الأقل
- مساحة تخزين: 50 جيجابايت متاحة
- معالج: Intel/AMD 64-bit مع دعم المحاكاة الافتراضية

## خطوات التثبيت

### الخطوة 1: تثبيت Docker

#### الطريقة الأولى: التثبيت التلقائي
```powershell
# تشغيل PowerShell كمدير
# انتقل إلى مجلد المشروع
cd C:\path\to\Origin-Certificate

# تشغيل سكريبت التثبيت
.\install-docker-windows-server.ps1
```

#### الطريقة الثانية: التثبيت اليدوي

1. **تفعيل Hyper-V:**
   ```powershell
   # تشغيل PowerShell كمدير
   Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All
   ```

2. **تفعيل Containers:**
   ```powershell
   Enable-WindowsOptionalFeature -Online -FeatureName Containers -All
   ```

3. **تحميل Docker Desktop:**
   - اذهب إلى: https://www.docker.com/products/docker-desktop
   - حمل النسخة المناسبة لـ Windows
   - قم بالتثبيت واتبع التعليمات

4. **إعادة تشغيل الجهاز**

### الخطوة 2: التحقق من تثبيت Docker

```powershell
# التحقق من إصدار Docker
docker --version

# التحقق من Docker Compose
docker-compose --version

# اختبار Docker
docker run hello-world
```

### الخطوة 3: إعداد المشروع

1. **نسخ ملف الإعدادات:**
   ```powershell
   Copy-Item .env.example .env
   ```

2. **تعديل الإعدادات:**
   ```powershell
   notepad .env
   ```

   عدل القيم التالية:
   ```env
   SECRET_KEY=your-unique-secret-key-here
   ALLOWED_HOSTS=localhost,127.0.0.1,your-server-ip
   DB_PASSWORD=your-secure-database-password
   ```

### الخطوة 4: تشغيل النظام

#### للتشغيل السريع:
```powershell
.\start-docker.ps1
```

#### للتشغيل اليدوي:
```powershell
# بناء وتشغيل الحاويات
docker-compose up --build -d

# انتظار بدء الخدمات
Start-Sleep -Seconds 30

# إعداد قاعدة البيانات
docker-compose exec web python manage.py migrate

# إنشاء مستخدم إداري
docker-compose exec web python manage.py createsuperuser
```

## التحقق من التثبيت

### 1. فحص الخدمات:
```powershell
docker-compose ps
```

### 2. فحص السجلات:
```powershell
docker-compose logs
```

### 3. اختبار الوصول:
- افتح المتصفح واذهب إلى: http://localhost:8000
- للوحة الإدارة: http://localhost:8000/admin

## إعدادات الإنتاج

### 1. تأمين النظام:

```env
# في ملف .env
DEBUG=False
SECRET_KEY=your-very-secure-secret-key
ALLOWED_HOSTS=your-domain.com,your-server-ip
SECURE_SSL_REDIRECT=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
```

### 2. إعداد قاعدة البيانات الخارجية:

```env
DATABASE_URL=**************************************************/database_name
```

### 3. إعداد النسخ الاحتياطي:

```powershell
# إنشاء مهمة مجدولة للنسخ الاحتياطي
$action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\path\to\docker-maintenance.ps1 -Action backup"
$trigger = New-ScheduledTaskTrigger -Daily -At 2AM
Register-ScheduledTask -Action $action -Trigger $trigger -TaskName "OriginCertificateBackup"
```

## استكشاف الأخطاء

### مشكلة: Docker لا يعمل
**الحل:**
```powershell
# إعادة تشغيل خدمة Docker
Restart-Service docker

# أو إعادة تشغيل Docker Desktop
```

### مشكلة: خطأ في الأذونات
**الحل:**
```powershell
# تشغيل PowerShell كمدير
# إعادة تعيين الأذونات
docker-compose exec web chown -R app:app /app
```

### مشكلة: فشل الاتصال بقاعدة البيانات
**الحل:**
```powershell
# فحص حالة قاعدة البيانات
docker-compose ps db

# إعادة تشغيل قاعدة البيانات
docker-compose restart db
```

### مشكلة: البورت مستخدم
**الحل:**
```powershell
# العثور على العملية التي تستخدم البورت
netstat -ano | findstr :8000

# إيقاف العملية
taskkill /PID <process_id> /F
```

## الصيانة الدورية

### النسخ الاحتياطي:
```powershell
.\docker-maintenance.ps1 -Action backup
```

### تحديث النظام:
```powershell
# إيقاف النظام
docker-compose down

# تحديث الكود
git pull

# إعادة البناء والتشغيل
docker-compose up --build -d
```

### تنظيف الموارد:
```powershell
.\docker-maintenance.ps1 -Action clean
```

## الدعم الفني

في حالة مواجهة مشاكل:

1. تحقق من السجلات: `docker-compose logs`
2. راجع هذا الدليل
3. تأكد من صحة إعدادات `.env`
4. تحقق من تشغيل Docker بشكل صحيح
