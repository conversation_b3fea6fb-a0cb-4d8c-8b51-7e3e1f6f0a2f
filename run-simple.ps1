Write-Host "=== Origin Certificate System - Simple Start ===" -ForegroundColor Green

Write-Host "Checking Docker..." -ForegroundColor Yellow
docker --version

Write-Host "`nTrying to start containers..." -ForegroundColor Yellow

Write-Host "Creating network..." -ForegroundColor Cyan
docker network create origin_network

Write-Host "Starting database..." -ForegroundColor Cyan
docker run -d --name origin_db --network origin_network -e POSTGRES_DB=origin_certificate -e POSTGRES_USER=django_user -e POSTGRES_PASSWORD=django_password_2024 -p 5432:5432 postgres:15

Write-Host "Building web app..." -ForegroundColor Cyan
docker build -f Dockerfile.production -t origin-web .

Write-Host "Starting web app..." -ForegroundColor Cyan
docker run -d --name origin_web --network origin_network -e DATABASE_URL=************************************************************/origin_certificate -p 8000:8000 origin-web

Write-Host "Waiting for startup..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

Write-Host "Running migrations..." -ForegroundColor Cyan
docker exec origin_web python manage.py migrate

Write-Host "`nContainers status:" -ForegroundColor Green
docker ps

Write-Host "`nTry accessing: http://localhost:8000" -ForegroundColor Cyan
