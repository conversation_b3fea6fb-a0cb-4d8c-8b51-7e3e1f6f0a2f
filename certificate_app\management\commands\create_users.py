from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from django.core.management.base import BaseCommand
from certificate_app.models import Branch, UserProfile

class Command(BaseCommand):
    help = 'Create initial users for all branches'

    def handle(self, *args, **kwargs):
        User = get_user_model()
        branch_names = ['محطة الرمل', 'السلطان حسين', 'الاستثماري']

        # Create branches if they don't exist
        branches = {}
        for branch_name in branch_names:
            branch, created = Branch.objects.get_or_create(name=branch_name)
            branches[branch_name] = branch

        # Create groups if they don't exist (dependency fix)
        Group.objects.get_or_create(name='super_admin')
        for branch_name in branch_names:
            Group.objects.get_or_create(name=f'{branch_name}_admin')
            Group.objects.get_or_create(name=f'{branch_name}_user')

        # Super Admin
        super_admin, created = User.objects.get_or_create(
            username='super_admin',
            defaults={'is_superuser': True, 'is_staff': True}
        )
        if created or not super_admin.check_password('securepassword123'):
            super_admin.set_password('securepassword123')
            super_admin.save()

        # Create UserProfile for super admin
        profile, created = UserProfile.objects.get_or_create(
            user=super_admin,
            defaults={'branch': None, 'is_branch_admin': False, 'is_branch_user': False}
        )
        super_admin.groups.add(Group.objects.get(name='super_admin'))

        # Branch Admins and Users
        for branch_name in branch_names:
            # Generate safe username (replace spaces with underscores)
            safe_branch = branch_name.replace(' ', '_')

            # Branch Admin
            admin_username = f'{safe_branch}_admin'
            admin, created = User.objects.get_or_create(
                username=admin_username,
                defaults={
                    'is_staff': True,
                    'is_superuser': False
                }
            )
            if created or not admin.check_password('securepassword123'):
                admin.set_password('securepassword123')
                admin.save()

            # Create UserProfile for branch admin
            profile, created = UserProfile.objects.get_or_create(
                user=admin,
                defaults={
                    'branch': branches[branch_name],
                    'is_branch_admin': True,
                    'is_branch_user': False
                }
            )
            admin.groups.add(Group.objects.get(name=f'{branch_name}_admin'))

            # Branch User
            user_username = f'{safe_branch}_user'
            user, created = User.objects.get_or_create(
                username=user_username,
                defaults={
                    'is_staff': False,
                    'is_superuser': False
                }
            )
            if created or not user.check_password('securepassword123'):
                user.set_password('securepassword123')
                user.save()

            # Create UserProfile for branch user
            profile, created = UserProfile.objects.get_or_create(
                user=user,
                defaults={
                    'branch': branches[branch_name],
                    'is_branch_admin': False,
                    'is_branch_user': True
                }
            )
            user.groups.add(Group.objects.get(name=f'{branch_name}_user'))

        self.stdout.write(self.style.SUCCESS('Users created successfully'))