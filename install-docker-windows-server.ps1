# PowerShell script to install Docker on Windows Server 2019
# Run as Administrator

Write-Host "=== Installing Docker on Windows Server 2019 ===" -ForegroundColor Green

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "Error: This script must be run as Administrator" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

# Check Windows version
$osVersion = [System.Environment]::OSVersion.Version
Write-Host "Windows Version: $($osVersion.Major).$($osVersion.Minor)" -ForegroundColor Cyan

if ($osVersion.Major -lt 10) {
    Write-Host "Error: Docker requires Windows 10 or Windows Server 2016/2019/2022" -ForegroundColor Red
    exit 1
}

# Install Hyper-V feature (required for Docker)
Write-Host "Installing Hyper-V feature..." -ForegroundColor Yellow
try {
    Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All -NoRestart
    Write-Host "Hyper-V feature installed successfully" -ForegroundColor Green
} catch {
    Write-Host "Warning: Could not install Hyper-V feature. You may need to enable it manually." -ForegroundColor Yellow
}

# Install Containers feature
Write-Host "Installing Containers feature..." -ForegroundColor Yellow
try {
    Enable-WindowsOptionalFeature -Online -FeatureName Containers -All -NoRestart
    Write-Host "Containers feature installed successfully" -ForegroundColor Green
} catch {
    Write-Host "Warning: Could not install Containers feature. You may need to enable it manually." -ForegroundColor Yellow
}

# Download and install Docker Desktop
Write-Host "Downloading Docker Desktop..." -ForegroundColor Yellow
$dockerUrl = "https://desktop.docker.com/win/main/amd64/Docker%20Desktop%20Installer.exe"
$dockerInstaller = "$env:TEMP\DockerDesktopInstaller.exe"

try {
    Invoke-WebRequest -Uri $dockerUrl -OutFile $dockerInstaller
    Write-Host "Docker Desktop downloaded successfully" -ForegroundColor Green
} catch {
    Write-Host "Error downloading Docker Desktop. Please download manually from:" -ForegroundColor Red
    Write-Host $dockerUrl -ForegroundColor Yellow
    exit 1
}

# Install Docker Desktop
Write-Host "Installing Docker Desktop..." -ForegroundColor Yellow
try {
    Start-Process -FilePath $dockerInstaller -ArgumentList "install --quiet" -Wait
    Write-Host "Docker Desktop installed successfully" -ForegroundColor Green
} catch {
    Write-Host "Error installing Docker Desktop. Please install manually." -ForegroundColor Red
    exit 1
}

# Clean up installer
Remove-Item $dockerInstaller -Force

Write-Host "=== Installation Complete ===" -ForegroundColor Green
Write-Host "IMPORTANT: You need to restart your computer for the changes to take effect." -ForegroundColor Yellow
Write-Host "After restart:" -ForegroundColor Cyan
Write-Host "1. Start Docker Desktop from the Start menu" -ForegroundColor White
Write-Host "2. Wait for Docker to start (check system tray)" -ForegroundColor White
Write-Host "3. Run 'docker version' to verify installation" -ForegroundColor White
Write-Host "4. Then you can run the Origin Certificate System" -ForegroundColor White

$restart = Read-Host "Do you want to restart now? (y/n)"
if ($restart -eq "y" -or $restart -eq "Y") {
    Write-Host "Restarting computer..." -ForegroundColor Yellow
    Restart-Computer -Force
} else {
    Write-Host "Please restart your computer manually to complete the installation." -ForegroundColor Yellow
}
