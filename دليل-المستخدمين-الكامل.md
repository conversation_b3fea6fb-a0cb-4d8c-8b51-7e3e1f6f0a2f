# 📚 الدليل الكامل لتشغيل نظام شهادات المنشأ

## 🚀 خطوات التشغيل الكاملة

### 1️⃣ تشغيل النظام
```powershell
.\RUN-SYSTEM.ps1
```

### 2️⃣ إنشاء المستخدمين (إذا لم يتم تلقائياً)
```powershell
.\create-users.ps1
```

### 3️⃣ الوصول للنظام
- **التطبيق:** http://localhost:8000
- **الإدارة:** http://localhost:8000/admin

---

## 🔐 معلومات تسجيل الدخول

### 👑 المدير العام
- **المستخدم:** `super_admin`
- **المرور:** `securepassword123`
- **الصلاحيات:** جميع الصلاحيات

### 🏢 مديري الفروع

| الفرع | اسم المستخدم | كلمة المرور |
|-------|-------------|------------|
| محطة الرمل | `محطة_الرمل_admin` | `securepassword123` |
| السلطان حسين | `السلطان_حسين_admin` | `securepassword123` |
| الاستثماري | `الاستثماري_admin` | `securepassword123` |

### 👥 مستخدمي الفروع

| الفرع | اسم المستخدم | كلمة المرور |
|-------|-------------|------------|
| محطة الرمل | `محطة_الرمل_user` | `securepassword123` |
| السلطان حسين | `السلطان_حسين_user` | `securepassword123` |
| الاستثماري | `الاستثماري_user` | `securepassword123` |

---

## 📁 الملفات المهمة

| الملف | الوصف |
|-------|--------|
| `RUN-SYSTEM.ps1` | **تشغيل النظام الكامل** |
| `create-users.ps1` | **إنشاء المستخدمين** |
| `معلومات-تسجيل-الدخول.md` | **دليل المستخدمين** |
| `خطوات-التشغيل.md` | **دليل التشغيل المفصل** |
| `start-docker.ps1` | تشغيل متقدم |
| `run-simple.ps1` | تشغيل مبسط |
| `diagnose.ps1` | تشخيص المشاكل |

---

## 🎯 سيناريوهات الاستخدام

### 🆕 التشغيل لأول مرة
```powershell
# 1. تشغيل النظام
.\RUN-SYSTEM.ps1

# 2. إنشاء المستخدمين (سيُسأل تلقائياً)
# أو يدوياً: .\create-users.ps1

# 3. الوصول للنظام
# http://localhost:8000
```

### 🔄 التشغيل اليومي
```powershell
# إذا كان النظام متوقف
.\RUN-SYSTEM.ps1

# أو إذا كان يعمل
# فقط افتح: http://localhost:8000
```

### 🛠️ الصيانة
```powershell
# عرض السجلات
docker logs origin_web -f

# إعادة تشغيل
docker restart origin_web origin_db

# إيقاف النظام
docker stop origin_web origin_db
```

---

## 🔧 حل المشاكل السريع

### ❌ المشكلة: النظام لا يعمل
```powershell
# 1. تشخيص المشكلة
.\diagnose.ps1

# 2. إعادة تشغيل Docker Desktop
# ابحث عن "Docker Desktop" في قائمة Start

# 3. إعادة تشغيل النظام
.\RUN-SYSTEM.ps1
```

### ❌ المشكلة: لا يمكن تسجيل الدخول
```powershell
# إعادة إنشاء المستخدمين
.\create-users.ps1
```

### ❌ المشكلة: البورت مستخدم
```powershell
# العثور على العملية
netstat -ano | findstr :8000

# إيقاف العملية
taskkill /PID <رقم_العملية> /F
```

---

## 📊 مستويات الصلاحيات

### 👑 المدير العام
- ✅ **جميع الصلاحيات**
- ✅ إدارة المستخدمين
- ✅ إدارة الفروع
- ✅ عرض جميع الشهادات
- ✅ التقارير الشاملة

### 🏢 مدير الفرع
- ✅ إدارة مستخدمي فرعه
- ✅ عرض شهادات فرعه
- ✅ تقارير فرعه
- ❌ الوصول لفروع أخرى

### 👤 مستخدم الفرع
- ✅ إضافة شهادات لفرعه
- ✅ تعديل شهاداته
- ✅ عرض شهادات فرعه
- ❌ حذف الشهادات
- ❌ إدارة المستخدمين

---

## 🔒 الأمان

### ⚠️ مهم جداً:
1. **غير كلمات المرور** فور التشغيل الأول
2. **لا تشارك معلومات الدخول** مع غير المخولين
3. **راجع الصلاحيات** بانتظام
4. **احتفظ بنسخ احتياطية** من البيانات

### 🔄 تغيير كلمة المرور:
```powershell
# من سطر الأوامر
docker exec -it origin_web python manage.py changepassword اسم_المستخدم

# أو من لوحة الإدارة
# http://localhost:8000/admin
```

---

## 📞 الدعم الفني

### 🔍 أدوات التشخيص:
```powershell
.\diagnose.ps1           # تشخيص شامل
docker ps               # حالة الحاويات
docker logs origin_web  # سجلات التطبيق
```

### 📚 المراجع:
- `TROUBLESHOOTING.md` - دليل استكشاف الأخطاء
- `README.md` - الدليل الشامل
- `INSTALLATION_GUIDE.md` - دليل التثبيت

---

## ✅ قائمة التحقق النهائية

- [ ] تم تشغيل `.\RUN-SYSTEM.ps1` بنجاح
- [ ] تم إنشاء المستخدمين باستخدام `.\create-users.ps1`
- [ ] يمكن الوصول لـ http://localhost:8000
- [ ] يمكن تسجيل الدخول بحساب `super_admin`
- [ ] تم تغيير كلمات المرور الافتراضية
- [ ] تم اختبار صلاحيات المستخدمين

**🎉 إذا تمت كل هذه الخطوات، فالنظام جاهز للاستخدام!**
