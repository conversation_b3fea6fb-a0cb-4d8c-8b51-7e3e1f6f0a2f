# 🚀 خطوات تشغيل نظام شهادات المنشأ

## 📋 الخطوات الكاملة للتشغيل

### الخطوة 1: تشغيل Docker Desktop

1. **اضغط على مفتاح Windows** (⊞)
2. **اكتب "Docker Desktop"** في مربع البحث
3. **اضغط على Docker Desktop** لتشغيله
4. **انتظر حتى يبدأ Docker Desktop** (قد يستغرق 2-5 دقائق)
5. **تحقق من أيقونة Docker** في الزاوية اليمنى السفلى (system tray)
6. **انتظر حتى تصبح الأيقونة خضراء** 🟢

### الخطوة 2: اختبار Docker

افتح **PowerShell** وشغل:

```powershell
docker ps
```

**النتيجة المتوقعة:**
```
CONTAINER ID   IMAGE     COMMAND   CREATED   STATUS    PORTS     NAMES
```
(قائمة فارغة - هذا طبيعي)

**إذا ظهر خطأ "Access denied"** ← Docker Desktop لم يبدأ بعد، انتظر أكثر

### الخطوة 3: الانتقال لمجلد المشروع

```powershell
cd C:\Users\<USER>\Desktop\Origin-Certificate
```

### الخطوة 4: تشغيل المشروع

**جرب الأمر الأول:**
```powershell
.\start-docker.ps1
```

**إذا فشل، جرب الأمر الثاني:**
```powershell
.\run-simple.ps1
```

### الخطوة 5: انتظار التشغيل

- **انتظر 2-3 دقائق** حتى يكتمل تحميل وبناء المشروع
- ستظهر رسائل مثل:
  ```
  ✓ Docker is running...
  Building and starting containers...
  ✓ Services Started Successfully
  ```

### الخطوة 6: الوصول للنظام

افتح المتصفح واذهب إلى:

- **التطبيق الرئيسي:** http://localhost:8000
- **لوحة الإدارة:** http://localhost:8000/admin

---

## 🔧 حل المشاكل الشائعة

### مشكلة: "docker-compose is not recognized"
**الحل:** هذا طبيعي، السكريبت سيحل المشكلة تلقائياً

### مشكلة: "Access denied"
**الحل:** 
1. تأكد أن Docker Desktop يعمل
2. تأكد أن الأيقونة خضراء
3. انتظر دقيقة إضافية

### مشكلة: "Port 8000 is already in use"
**الحل:**
```powershell
# ابحث عن العملية التي تستخدم البورت
netstat -ano | findstr :8000

# أوقف العملية (استبدل PID برقم العملية)
taskkill /PID <رقم_العملية> /F
```

### مشكلة: Docker Desktop لا يبدأ
**الحل:**
1. أعد تشغيل الكمبيوتر
2. شغل PowerShell كمدير وشغل:
   ```powershell
   net start docker
   ```

---

## 📊 علامات النجاح

### عند نجاح التشغيل ستظهر:
```
🎉 Services Started Successfully
Web Application: http://localhost:8000
Database: localhost:5432
```

### في المتصفح ستظهر:
- صفحة تسجيل الدخول للنظام
- أو الصفحة الرئيسية للنظام

---

## 🛠️ أوامر مفيدة بعد التشغيل

### عرض حالة الحاويات:
```powershell
docker ps
```

### عرض السجلات:
```powershell
docker-compose logs -f
# أو
docker logs origin_web -f
```

### إنشاء مستخدم إداري:
```powershell
docker-compose exec web python manage.py createsuperuser
# أو
docker exec -it origin_web python manage.py createsuperuser
```

### إيقاف النظام:
```powershell
docker-compose down
# أو
docker stop origin_web origin_db
```

---

## 📞 طلب المساعدة

إذا واجهت مشاكل:

1. **شغل التشخيص:**
   ```powershell
   .\diagnose.ps1
   ```

2. **اجمع معلومات الخطأ:**
   ```powershell
   docker ps
   docker logs origin_web
   ```

3. **راجع دليل استكشاف الأخطاء:**
   ```powershell
   Get-Content TROUBLESHOOTING.md
   ```

---

## ✅ قائمة التحقق السريعة

- [ ] Docker Desktop مثبت ويعمل
- [ ] أيقونة Docker خضراء في system tray
- [ ] `docker ps` يعمل بدون أخطاء
- [ ] تم تشغيل `.\start-docker.ps1` أو `.\run-simple.ps1`
- [ ] انتظرت 2-3 دقائق للتشغيل
- [ ] http://localhost:8000 يفتح في المتصفح

**إذا تمت كل هذه الخطوات، فالنظام يعمل بنجاح! 🎉**
