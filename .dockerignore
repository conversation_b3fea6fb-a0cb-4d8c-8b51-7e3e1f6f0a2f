# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
OriginCertificateSystem/venv/
.venv
ENV/
env/
.env

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
README.md
*.md

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.exe
*.msi
*.bat

# Backup files
*.bak
*.backup
*.sql

# Temporary files
*.tmp
*.temp
