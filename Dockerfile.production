# Use the official Python runtime image
FROM python:3.13-slim

# Set environment variables 
# Prevents Python from writing pyc files to disk
ENV PYTHONDONTWRITEBYTECODE=1
# Prevents Python from buffering stdout and stderr
ENV PYTHONUNBUFFERED=1
# Set Django settings module
ENV DJANGO_SETTINGS_MODULE=OriginCertificateSystem.settings

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        postgresql-client \
        build-essential \
        libpq-dev \
        gettext \
        curl \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN addgroup --system app && adduser --system --group app

# Create the app directory
RUN mkdir /app && chown app:app /app

# Set the working directory inside the container
WORKDIR /app

# Upgrade pip
RUN python -m pip install --upgrade pip

# Copy the requirements file and install dependencies
COPY requirements.txt /app/
RUN python -m pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application code
COPY . /app/

# Change ownership of the app directory
RUN chown -R app:app /app

# Switch to app user
USER app

# Create necessary directories
RUN mkdir -p /app/static /app/media

# Expose the Django port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/ || exit 1

# Run Django with Gunicorn for production
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "3", "OriginCertificateSystem.wsgi:application"]
