# PowerShell script to start Origin Certificate System with Docker
# For Windows Server 2019

Write-Host "=== Origin Certificate System Docker Setup ===" -ForegroundColor Green

# Check if Docker is installed
if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Host "Error: Docker is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Docker Desktop for Windows first" -ForegroundColor Yellow
    exit 1
}

# Check if Docker is running
try {
    docker version | Out-Null
} catch {
    Write-Host "Error: Docker is not running" -ForegroundColor Red
    Write-Host "Please start Docker Desktop" -ForegroundColor Yellow
    exit 1
}

Write-Host "Docker is running..." -ForegroundColor Green

# Check if .env file exists
if (!(Test-Path ".env")) {
    Write-Host "Creating .env file from template..." -ForegroundColor Yellow
    Copy-Item ".env.example" ".env"
    Write-Host "Please edit .env file with your settings before continuing" -ForegroundColor Yellow
    Read-Host "Press Enter when ready to continue"
}

# Stop any existing containers
Write-Host "Stopping existing containers..." -ForegroundColor Yellow
docker-compose down

# Build and start the containers
Write-Host "Building and starting containers..." -ForegroundColor Green
docker-compose up --build -d

# Wait for services to start
Write-Host "Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Check if services are running
$webStatus = docker-compose ps web | Select-String "Up"
$dbStatus = docker-compose ps db | Select-String "Up"

if ($webStatus -and $dbStatus) {
    Write-Host "=== Services Started Successfully ===" -ForegroundColor Green
    Write-Host "Web Application: http://localhost:8000" -ForegroundColor Cyan
    Write-Host "Database: localhost:5432" -ForegroundColor Cyan
    Write-Host "Nginx (if enabled): http://localhost" -ForegroundColor Cyan
    
    Write-Host "`n=== Useful Commands ===" -ForegroundColor Yellow
    Write-Host "View logs: docker-compose logs -f" -ForegroundColor White
    Write-Host "Stop services: docker-compose down" -ForegroundColor White
    Write-Host "Restart services: docker-compose restart" -ForegroundColor White
    Write-Host "Access Django shell: docker-compose exec web python manage.py shell" -ForegroundColor White
    Write-Host "Create superuser: docker-compose exec web python manage.py createsuperuser" -ForegroundColor White
} else {
    Write-Host "Error: Some services failed to start" -ForegroundColor Red
    Write-Host "Check logs with: docker-compose logs" -ForegroundColor Yellow
}

Write-Host "`nPress Enter to view logs (Ctrl+C to exit logs)..." -ForegroundColor Yellow
Read-Host
docker-compose logs -f
