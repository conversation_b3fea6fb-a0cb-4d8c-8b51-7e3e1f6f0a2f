# PowerShell script to start Origin Certificate System with Docker
# For Windows Server 2019

Write-Host "=== Origin Certificate System Docker Setup ===" -ForegroundColor Green

# Check if Docker is installed
if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Host "Error: Docker is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Docker Desktop for Windows first" -ForegroundColor Yellow
    exit 1
}

# Check if Docker is running
try {
    $dockerVersion = docker version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker not accessible"
    }
} catch {
    Write-Host "Error: Docker is not running or not installed" -ForegroundColor Red
    Write-Host "Please install Docker Desktop first using:" -ForegroundColor Yellow
    Write-Host ".\install-docker-windows-server.ps1" -ForegroundColor Cyan
    exit 1
}

Write-Host "Docker is running..." -ForegroundColor Green

# Check if docker-compose is available
if (!(Get-Command docker-compose -ErrorAction SilentlyContinue)) {
    Write-Host "docker-compose not found. Trying 'docker compose'..." -ForegroundColor Yellow
    if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
        Write-Host "Error: Docker commands not available" -ForegroundColor Red
        exit 1
    }
    # Use docker compose instead of docker-compose
    $dockerComposeCmd = "docker compose"
} else {
    $dockerComposeCmd = "docker-compose"
}

Write-Host "Using: $dockerComposeCmd" -ForegroundColor Green

# Check if .env file exists
if (!(Test-Path ".env")) {
    Write-Host "Creating .env file from template..." -ForegroundColor Yellow
    Copy-Item ".env.example" ".env"
    Write-Host "Please edit .env file with your settings before continuing" -ForegroundColor Yellow
    Read-Host "Press Enter when ready to continue"
}

# Stop any existing containers
Write-Host "Stopping existing containers..." -ForegroundColor Yellow
Invoke-Expression "$dockerComposeCmd down"

# Build and start the containers
Write-Host "Building and starting containers..." -ForegroundColor Green
Invoke-Expression "$dockerComposeCmd up --build -d"

# Wait for services to start
Write-Host "Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Check if services are running
$webStatus = Invoke-Expression "$dockerComposeCmd ps web" | Select-String "Up"
$dbStatus = Invoke-Expression "$dockerComposeCmd ps db" | Select-String "Up"

if ($webStatus -and $dbStatus) {
    Write-Host "=== Services Started Successfully ===" -ForegroundColor Green
    Write-Host "Web Application: http://localhost:8000" -ForegroundColor Cyan
    Write-Host "Database: localhost:5432" -ForegroundColor Cyan
    Write-Host "Nginx (if enabled): http://localhost" -ForegroundColor Cyan

    Write-Host "`n=== Useful Commands ===" -ForegroundColor Yellow
    Write-Host "View logs: $dockerComposeCmd logs -f" -ForegroundColor White
    Write-Host "Stop services: $dockerComposeCmd down" -ForegroundColor White
    Write-Host "Restart services: $dockerComposeCmd restart" -ForegroundColor White
    Write-Host "Access Django shell: $dockerComposeCmd exec web python manage.py shell" -ForegroundColor White
    Write-Host "Create superuser: $dockerComposeCmd exec web python manage.py createsuperuser" -ForegroundColor White
} else {
    Write-Host "Error: Some services failed to start" -ForegroundColor Red
    Write-Host "Check logs with: $dockerComposeCmd logs" -ForegroundColor Yellow
}

Write-Host "`nPress Enter to view logs (Ctrl+C to exit logs)..." -ForegroundColor Yellow
Read-Host
Invoke-Expression "$dockerComposeCmd logs -f"
