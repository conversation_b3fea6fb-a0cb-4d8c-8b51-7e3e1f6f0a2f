# PowerShell script to start Origin Certificate System in Development Mode
# For Windows Server 2019

Write-Host "=== Origin Certificate System - Development Mode ===" -ForegroundColor Green

# Check if Docker is installed
if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Host "Error: Docker is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Docker Desktop for Windows first" -ForegroundColor Yellow
    exit 1
}

# Check if Docker is running
try {
    docker version | Out-Null
} catch {
    Write-Host "Error: Docker is not running" -ForegroundColor Red
    Write-Host "Please start Docker Desktop" -ForegroundColor Yellow
    exit 1
}

Write-Host "Docker is running..." -ForegroundColor Green

# Stop any existing containers
Write-Host "Stopping existing containers..." -ForegroundColor Yellow
docker-compose -f docker-compose.dev.yml down

# Build and start the development containers
Write-Host "Building and starting development containers..." -ForegroundColor Green
docker-compose -f docker-compose.dev.yml up --build -d

# Wait for services to start
Write-Host "Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 20

# Run migrations
Write-Host "Running database migrations..." -ForegroundColor Yellow
docker-compose -f docker-compose.dev.yml exec web python manage.py migrate

# Check if services are running
$webStatus = docker-compose -f docker-compose.dev.yml ps web | Select-String "Up"

if ($webStatus) {
    Write-Host "=== Development Environment Started Successfully ===" -ForegroundColor Green
    Write-Host "Web Application: http://localhost:8000" -ForegroundColor Cyan
    Write-Host "Admin Panel: http://localhost:8000/admin" -ForegroundColor Cyan
    
    Write-Host "`n=== Development Commands ===" -ForegroundColor Yellow
    Write-Host "View logs: docker-compose -f docker-compose.dev.yml logs -f" -ForegroundColor White
    Write-Host "Stop services: docker-compose -f docker-compose.dev.yml down" -ForegroundColor White
    Write-Host "Django shell: docker-compose -f docker-compose.dev.yml exec web python manage.py shell" -ForegroundColor White
    Write-Host "Create superuser: docker-compose -f docker-compose.dev.yml exec web python manage.py createsuperuser" -ForegroundColor White
    Write-Host "Run tests: docker-compose -f docker-compose.dev.yml exec web python manage.py test" -ForegroundColor White
    
    Write-Host "`nNote: Code changes will be automatically reloaded" -ForegroundColor Green
} else {
    Write-Host "Error: Services failed to start" -ForegroundColor Red
    Write-Host "Check logs with: docker-compose -f docker-compose.dev.yml logs" -ForegroundColor Yellow
}

Write-Host "`nPress Enter to view logs (Ctrl+C to exit logs)..." -ForegroundColor Yellow
Read-Host
docker-compose -f docker-compose.dev.yml logs -f
