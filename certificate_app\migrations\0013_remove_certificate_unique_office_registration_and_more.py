# Generated by Django 5.1.5 on 2025-05-20 08:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('certificate_app', '0012_certificate_unique_office_registration'),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name='certificate',
            name='unique_office_registration',
        ),
        migrations.AddConstraint(
            model_name='certificate',
            constraint=models.UniqueConstraint(condition=models.Q(('RegistrationNumber', 'غير موجود'), _negated=True), fields=('Branch', 'Office', 'RegistrationNumber', 'CertificateNumber'), name='unique_branch_office_reg_cert'),
        ),
    ]
