# Script to start Docker service and daemon

Write-Host "=== Starting Docker Service ===" -ForegroundColor Green

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script needs Administrator privileges to start Docker service" -ForegroundColor Yellow
    Write-Host "Restarting as Administrator..." -ForegroundColor Cyan
    Start-Process PowerShell -Verb RunAs -ArgumentList "-File `"$PSCommandPath`""
    exit
}

Write-Host "Running as Administrator..." -ForegroundColor Green

# Try to start Docker service
Write-Host "Starting Docker service..." -ForegroundColor Yellow
try {
    Start-Service docker -ErrorAction Stop
    Write-Host "✓ Docker service started" -ForegroundColor Green
} catch {
    Write-Host "⚠ Could not start Docker service: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Try to find and start Docker Desktop
Write-Host "Looking for Docker Desktop..." -ForegroundColor Yellow

$dockerPaths = @(
    "C:\Program Files\Docker\Docker\Docker Desktop.exe",
    "C:\Program Files (x86)\Docker\Docker\Docker Desktop.exe",
    "$env:LOCALAPPDATA\Programs\Docker\Docker\Docker Desktop.exe",
    "$env:PROGRAMFILES\Docker\Docker\Docker Desktop.exe"
)

$dockerFound = $false
foreach ($path in $dockerPaths) {
    if (Test-Path $path) {
        Write-Host "Found Docker Desktop at: $path" -ForegroundColor Green
        try {
            Start-Process $path
            Write-Host "✓ Docker Desktop started" -ForegroundColor Green
            $dockerFound = $true
            break
        } catch {
            Write-Host "⚠ Could not start Docker Desktop: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
}

if (-not $dockerFound) {
    Write-Host "Docker Desktop not found in common locations" -ForegroundColor Yellow
    Write-Host "Please start Docker Desktop manually:" -ForegroundColor Cyan
    Write-Host "1. Press Windows key" -ForegroundColor White
    Write-Host "2. Type 'Docker Desktop'" -ForegroundColor White
    Write-Host "3. Click on Docker Desktop" -ForegroundColor White
}

# Wait and check if Docker is running
Write-Host "`nWaiting for Docker to start..." -ForegroundColor Yellow
$maxWait = 60
$waited = 0

while ($waited -lt $maxWait) {
    try {
        docker version | Out-Null 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "🎉 Docker is now running!" -ForegroundColor Green
            break
        }
    } catch {}
    
    Start-Sleep -Seconds 5
    $waited += 5
    Write-Host "Still waiting... ($waited/$maxWait seconds)" -ForegroundColor Gray
}

# Final check
try {
    docker version | Out-Null 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`n✓ Docker is ready!" -ForegroundColor Green
        Write-Host "You can now run: .\start-without-compose.ps1" -ForegroundColor Cyan
    } else {
        Write-Host "`n✗ Docker is still not running" -ForegroundColor Red
        Write-Host "Please check Docker Desktop manually" -ForegroundColor Yellow
    }
} catch {
    Write-Host "`n✗ Docker is still not running" -ForegroundColor Red
    Write-Host "Please check Docker Desktop manually" -ForegroundColor Yellow
}

Write-Host "`nPress Enter to continue..." -ForegroundColor Gray
Read-Host
