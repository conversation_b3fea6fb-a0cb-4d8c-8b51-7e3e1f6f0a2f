# Generated by Django 5.1.5 on 2025-05-27 12:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('certificate_app', '0017_remove_certificate_importcompanyaddress_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='company',
            name='importCompanyAddress',
        ),
        migrations.RemoveField(
            model_name='company',
            name='importCompanyName',
        ),
        migrations.RemoveField(
            model_name='company',
            name='importCompanyPhone',
        ),
        migrations.AddField(
            model_name='certificate',
            name='importCompanyAddress',
            field=models.Char<PERSON>ield(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='certificate',
            name='importCompanyName',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='certificate',
            name='importCompanyPhone',
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True),
        ),
    ]
