# Generated by Django 5.1.5 on 2025-04-21 17:57

import django.contrib.auth.models
import django.contrib.auth.validators
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Cargo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ExportedGoods', models.CharField(max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('CompanyName', models.CharField(max_length=100)),
                ('CompanyAddress', models.Char<PERSON><PERSON>(max_length=200)),
                ('CompanyType', models.CharField(max_length=50)),
                ('CompanyStatus', models.CharField(max_length=50)),
                ('importCompanyName', models.CharField(blank=True, max_length=100, null=True)),
                ('importCompanyAddress', models.CharField(blank=True, max_length=200, null=True)),
                ('importCompanyPhone', models.CharField(blank=True, max_length=50, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Country',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('CountryName', models.CharField(max_length=100, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='Office',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('OfficeName', models.CharField(blank=True, max_length=100, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Certificate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('BranchName', models.CharField(choices=[('محطة الرمل', 'محطة الرمل'), ('السلطان حسين', 'السلطان حسين'), ('الاستثماري', 'الاستثماري')], max_length=255)),
                ('RegistrationNumber', models.CharField(blank=True, max_length=50, null=True)),
                ('CertificateNumber', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('IssueDate', models.DateField()),
                ('ReceiptNumber', models.CharField(blank=True, max_length=50, null=True)),
                ('ReceiptDate', models.DateField(blank=True, null=True)),
                ('PaymentAmount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('quantity_unit', models.CharField(choices=[('طن', 'طن'), ('كجم', 'كجم'), ('وحده', 'وحده')], default='كجم', max_length=10)),
                ('default_currency', models.CharField(default='USD', max_length=3)),
                ('Company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='certificate_app.company')),
                ('ExportCountry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='export_country_certificates', to='certificate_app.country')),
                ('OriginCountry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='origin_country_certificates', to='certificate_app.country')),
                ('Office', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='certificate_app.office')),
            ],
        ),
        migrations.CreateModel(
            name='CustomUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('branch', models.CharField(blank=True, choices=[('محطة الرمل', 'محطة الرمل'), ('السلطان حسين', 'السلطان حسين'), ('الاستثماري', 'الاستثماري')], help_text='The branch this user is associated with. Leave blank for super admins.', max_length=255, null=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
                ('office', models.ForeignKey(blank=True, help_text='Optional office assignment for the user.', null=True, on_delete=django.db.models.deletion.SET_NULL, to='certificate_app.office')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'abstract': False,
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='Shipment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10)),
                ('cost_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('cargo', models.ForeignKey(help_text='Select the cargo from the predefined Cargo table.', on_delete=django.db.models.deletion.CASCADE, to='certificate_app.cargo')),
                ('certificate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shipments', to='certificate_app.certificate')),
            ],
        ),
        migrations.AddIndex(
            model_name='certificate',
            index=models.Index(fields=['BranchName'], name='certificate_BranchN_05dd90_idx'),
        ),
    ]
