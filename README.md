# نظام شهادات المنشأ (Origin Certificate System)

نظام إدارة شهادات المنشأ مبني بـ Django لإدارة وإصدار شهادات المنشأ للشركات والبضائع.

## المميزات

- إدارة المستخدمين والفروع
- إصدار وإدارة شهادات المنشأ
- نظام تقارير شامل
- واجهة إدارة متقدمة
- دعم اللغة العربية
- تصدير البيانات بصيغ مختلفة

## متطلبات النظام

### للتشغيل على Windows Server 2019:

1. **Docker Desktop for Windows**
   - تحميل من: https://www.docker.com/products/docker-desktop
   - يتطلب Windows 10/11 أو Windows Server 2019/2022
   - يتطلب Hyper-V أو WSL2

2. **PowerShell 5.0 أو أحدث** (متوفر افتراضياً)

3. **Git** (اختياري لتحديث المشروع)

## التثبيت والتشغيل

### الطريقة الأولى: التشغيل السريع

1. **تحضير الملفات:**
   ```powershell
   # انسخ ملف الإعدادات
   Copy-Item .env.example .env

   # عدل الإعدادات حسب احتياجاتك
   notepad .env
   ```

2. **تشغيل النظام:**
   ```powershell
   # تشغيل سكريبت التثبيت التلقائي
   .\start-docker.ps1
   ```

### الطريقة الثانية: التشغيل اليدوي

1. **بناء وتشغيل الحاويات:**
   ```powershell
   # بناء الصور
   docker-compose build

   # تشغيل الخدمات
   docker-compose up -d
   ```

2. **إعداد قاعدة البيانات:**
   ```powershell
   # تشغيل الترحيلات
   docker-compose exec web python manage.py migrate

   # إنشاء مستخدم إداري
   docker-compose exec web python manage.py createsuperuser
   ```

## الوصول للنظام

بعد التشغيل الناجح:

- **التطبيق الرئيسي:** http://localhost:8000
- **لوحة الإدارة:** http://localhost:8000/admin
- **قاعدة البيانات:** localhost:5432

## إدارة النظام

### أوامر الصيانة الأساسية:

```powershell
# عرض السجلات
docker-compose logs -f

# إعادة تشغيل الخدمات
docker-compose restart

# إيقاف النظام
docker-compose down

# تنظيف الموارد
docker-compose down -v
docker system prune -f
```

### استخدام سكريبت الصيانة:

```powershell
# إنشاء نسخة احتياطية
.\docker-maintenance.ps1 -Action backup

# استعادة نسخة احتياطية
.\docker-maintenance.ps1 -Action restore -BackupFile backup_20240101_120000.sql

# فتح Django shell
.\docker-maintenance.ps1 -Action shell

# تشغيل الترحيلات
.\docker-maintenance.ps1 -Action migrate

# إنشاء مستخدم إداري
.\docker-maintenance.ps1 -Action createsuperuser
```

## هيكل المشروع

```
Origin-Certificate/
├── certificate_app/          # تطبيق Django الرئيسي
├── frontend/                 # ملفات الواجهة الأمامية
├── OriginCertificateSystem/   # إعدادات Django
├── docker-compose.yml        # إعدادات Docker Compose
├── Dockerfile                # Dockerfile الأساسي
├── Dockerfile.production     # Dockerfile للإنتاج
├── nginx.conf               # إعدادات Nginx
├── requirements.txt         # متطلبات Python
├── start-docker.ps1         # سكريبت التشغيل
├── docker-maintenance.ps1   # سكريبت الصيانة
├── .env.example            # مثال على متغيرات البيئة
└── README.md               # هذا الملف
```

## الإعدادات المتقدمة

### تخصيص قاعدة البيانات:

لاستخدام PostgreSQL بدلاً من SQLite، عدل ملف `.env`:

```env
DATABASE_URL=*****************************************************/origin_certificate
```

### تخصيص الأمان:

لبيئة الإنتاج، تأكد من تغيير:

```env
SECRET_KEY=your-very-secure-secret-key-here
DEBUG=False
ALLOWED_HOSTS=your-domain.com,your-ip-address
```

### تفعيل HTTPS:

```env
SECURE_SSL_REDIRECT=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

1. **خطأ في الاتصال بقاعدة البيانات:**
   ```powershell
   # تحقق من حالة قاعدة البيانات
   docker-compose ps db

   # عرض سجلات قاعدة البيانات
   docker-compose logs db
   ```

2. **خطأ في تشغيل التطبيق:**
   ```powershell
   # عرض سجلات التطبيق
   docker-compose logs web

   # إعادة بناء الصورة
   docker-compose build --no-cache web
   ```

3. **مشاكل الأذونات:**
   ```powershell
   # إعادة تعيين الأذونات
   docker-compose exec web chown -R app:app /app
   ```

## النسخ الاحتياطي والاستعادة

### إنشاء نسخة احتياطية تلقائية:

```powershell
# إنشاء مهمة مجدولة للنسخ الاحتياطي اليومي
$action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\path\to\docker-maintenance.ps1 -Action backup"
$trigger = New-ScheduledTaskTrigger -Daily -At 2AM
Register-ScheduledTask -Action $action -Trigger $trigger -TaskName "OriginCertificateBackup"
```

## الدعم والمساعدة

للحصول على المساعدة:

1. تحقق من السجلات: `docker-compose logs`
2. راجع هذا الدليل
3. تحقق من إعدادات `.env`
4. تأكد من تشغيل Docker بشكل صحيح

## الترخيص

هذا المشروع مخصص لنظام شهادات المنشأ.
