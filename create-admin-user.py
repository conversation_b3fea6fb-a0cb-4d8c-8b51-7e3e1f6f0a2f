#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create admin user for Origin Certificate System
Run this script in the Django project directory
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OriginCertificateSystem.settings')
django.setup()

from django.contrib.auth.models import User
from django.db import IntegrityError

def create_admin_users():
    """Create admin users for the system"""
    
    users_to_create = [
        {
            'username': 'admin',
            'email': '<EMAIL>',
            'password': 'admin123',
            'first_name': 'System',
            'last_name': 'Administrator',
            'is_superuser': True
        },
        {
            'username': 'super_admin',
            'email': '<EMAIL>', 
            'password': 'securepassword123',
            'first_name': 'Super',
            'last_name': 'Admin',
            'is_superuser': True
        },
        {
            'username': 'root',
            'email': '<EMAIL>',
            'password': 'root123',
            'first_name': 'Root',
            'last_name': 'User',
            'is_superuser': True
        }
    ]
    
    print("🔐 Creating admin users for Origin Certificate System...")
    print("=" * 60)
    
    for user_data in users_to_create:
        try:
            # Check if user already exists
            if User.objects.filter(username=user_data['username']).exists():
                print(f"⚠️  User '{user_data['username']}' already exists")
                # Update password for existing user
                user = User.objects.get(username=user_data['username'])
                user.set_password(user_data['password'])
                user.is_superuser = True
                user.is_staff = True
                user.save()
                print(f"✅ Updated password for '{user_data['username']}'")
            else:
                # Create new user
                user = User.objects.create_superuser(
                    username=user_data['username'],
                    email=user_data['email'],
                    password=user_data['password'],
                    first_name=user_data['first_name'],
                    last_name=user_data['last_name']
                )
                print(f"✅ Created user '{user_data['username']}'")
                
        except IntegrityError as e:
            print(f"❌ Error creating user '{user_data['username']}': {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 Admin user creation completed!")
    print("\n📋 Login Information:")
    print("🌐 Admin Panel: http://***************:8010/admin/")
    print("🌐 Main Site: http://***************:8010/")
    print("\n👤 Available Admin Accounts:")
    
    for user_data in users_to_create:
        print(f"   Username: {user_data['username']}")
        print(f"   Password: {user_data['password']}")
        print("   " + "-" * 30)

if __name__ == "__main__":
    try:
        create_admin_users()
    except Exception as e:
        print(f"❌ Script failed: {e}")
        print("Make sure you're running this from the Django project directory")
        sys.exit(1)
