# Environment variables for Origin Certificate System
# Copy this file to .env and modify the values as needed

# Django settings
DEBUG=False
SECRET_KEY=your-secret-key-here-change-this-in-production
DJANGO_SETTINGS_MODULE=OriginCertificateSystem.settings_production

# Allowed hosts (comma-separated)
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# Database settings (PostgreSQL)
DB_NAME=origin_certificate
DB_USER=django_user
DB_PASSWORD=django_password_2024
DB_HOST=db
DB_PORT=5432

# For local PostgreSQL (if not using Docker)
# DB_HOST=localhost

# PostgreSQL settings (for docker-compose)
POSTGRES_DB=origin_certificate
POSTGRES_USER=django_user
POSTGRES_PASSWORD=django_password_2024

# Database URL (alternative to individual DB settings)
DATABASE_URL=*****************************************************/origin_certificate

# Email settings (optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Security settings
SECURE_SSL_REDIRECT=False
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False
