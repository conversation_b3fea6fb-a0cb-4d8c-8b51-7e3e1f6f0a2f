# Quick system check for Origin Certificate System

Write-Host "=== Quick System Check ===" -ForegroundColor Green

# Check Docker
Write-Host "`nChecking Docker..." -ForegroundColor Yellow
if (Get-Command docker -ErrorAction SilentlyContinue) {
    Write-Host "✓ Docker command found" -ForegroundColor Green
    try {
        $version = docker --version
        Write-Host "✓ Docker version: $version" -ForegroundColor Green
        
        # Test if Docker daemon is running
        docker version | Out-Null 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Docker daemon is running" -ForegroundColor Green
        } else {
            Write-Host "✗ Docker daemon is not running" -ForegroundColor Red
            Write-Host "  Start Docker Desktop from Start menu" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "✗ Docker not accessible" -ForegroundColor Red
    }
} else {
    Write-Host "✗ Docker not installed" -ForegroundColor Red
    Write-Host "  Run: .\install-docker-simple.ps1" -ForegroundColor Yellow
}

# Check Docker Compose
Write-Host "`nChecking Docker Compose..." -ForegroundColor Yellow
if (Get-Command docker-compose -ErrorAction SilentlyContinue) {
    Write-Host "✓ docker-compose available" -ForegroundColor Green
} else {
    Write-Host "⚠ docker-compose not found, checking docker compose..." -ForegroundColor Yellow
    try {
        docker compose version | Out-Null 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ docker compose available" -ForegroundColor Green
        } else {
            Write-Host "✗ Docker Compose not available" -ForegroundColor Red
        }
    } catch {
        Write-Host "✗ Docker Compose not available" -ForegroundColor Red
    }
}

# Check essential files
Write-Host "`nChecking project files..." -ForegroundColor Yellow
$files = @("docker-compose.yml", "requirements.txt", "manage.py")
foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "✓ $file exists" -ForegroundColor Green
    } else {
        Write-Host "✗ $file missing" -ForegroundColor Red
    }
}

# Check .env file
if (Test-Path ".env") {
    Write-Host "✓ .env file exists" -ForegroundColor Green
} else {
    Write-Host "⚠ .env file missing" -ForegroundColor Yellow
    Write-Host "  Creating from template..." -ForegroundColor Cyan
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
        Write-Host "✓ .env file created" -ForegroundColor Green
    } else {
        Write-Host "✗ .env.example not found" -ForegroundColor Red
    }
}

# Check port 8000
Write-Host "`nChecking port 8000..." -ForegroundColor Yellow
try {
    $connection = New-Object System.Net.Sockets.TcpClient("localhost", 8000)
    $connection.Close()
    Write-Host "⚠ Port 8000 is in use" -ForegroundColor Yellow
} catch {
    Write-Host "✓ Port 8000 is available" -ForegroundColor Green
}

Write-Host "`n=== Summary ===" -ForegroundColor Cyan
Write-Host "Ready to start the system!" -ForegroundColor Green
Write-Host "Run: .\start-docker.ps1" -ForegroundColor White
