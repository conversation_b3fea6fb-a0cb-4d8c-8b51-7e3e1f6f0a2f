version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15
    restart: unless-stopped
    environment:
      POSTGRES_DB: origin_certificate
      POSTGRES_USER: django_user
      POSTGRES_PASSWORD: django_password_2024
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - origin_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U django_user -d origin_certificate"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Django Application
  web:
    build:
      context: .
      dockerfile: Dockerfile.production
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - DATABASE_URL=*****************************************************/origin_certificate
      - ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
      - DJ<PERSON>GO_SETTINGS_MODULE=OriginCertificateSystem.settings
    volumes:
      - static_volume:/app/static
      - media_volume:/app/media
    depends_on:
      db:
        condition: service_healthy
    networks:
      - origin_network
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             gunicorn --bind 0.0.0.0:8000 --workers 3 OriginCertificateSystem.wsgi:application"

  # Nginx Reverse Proxy (Optional for production)
  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - static_volume:/app/static
      - media_volume:/app/media
    depends_on:
      - web
    networks:
      - origin_network

volumes:
  postgres_data:
  static_volume:
  media_volume:

networks:
  origin_network:
    driver: bridge
