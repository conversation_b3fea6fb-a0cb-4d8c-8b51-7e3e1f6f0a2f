# ملف تشغيل نظام شهادات المنشأ - خطوة بخطوة
# Origin Certificate System - Step by Step Runner

Write-Host "🚀 مرحباً بك في نظام شهادات المنشأ" -ForegroundColor Green
Write-Host "Origin Certificate System - Step by Step Setup" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Cyan

# الخطوة 1: فحص Docker
Write-Host "`n📋 الخطوة 1: فحص Docker" -ForegroundColor Yellow
Write-Host "Step 1: Checking Docker" -ForegroundColor Gray

if (Get-Command docker -ErrorAction SilentlyContinue) {
    Write-Host "✓ Docker مثبت" -ForegroundColor Green
    Write-Host "✓ Docker is installed" -ForegroundColor Gray

    # فحص إصدار Docker
    $dockerVersion = docker --version
    Write-Host "الإصدار: $dockerVersion" -ForegroundColor Cyan
} else {
    Write-Host "✗ Docker غير مثبت" -ForegroundColor Red
    Write-Host "✗ Docker is not installed" -ForegroundColor Gray
    Write-Host "يرجى تثبيت Docker أولاً باستخدام: .\install-docker-simple.ps1" -ForegroundColor Yellow
    exit 1
}

# الخطوة 2: فحص Docker Daemon
Write-Host "`n📋 الخطوة 2: فحص Docker Daemon" -ForegroundColor Yellow
Write-Host "Step 2: Checking Docker Daemon" -ForegroundColor Gray

try {
    docker version | Out-Null 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Docker Daemon يعمل" -ForegroundColor Green
        Write-Host "✓ Docker Daemon is running" -ForegroundColor Gray
    } else {
        throw "Docker Daemon not running"
    }
} catch {
    Write-Host "✗ Docker Daemon لا يعمل" -ForegroundColor Red
    Write-Host "✗ Docker Daemon is not running" -ForegroundColor Gray
    Write-Host "`nيرجى تشغيل Docker Desktop:" -ForegroundColor Yellow
    Write-Host "Please start Docker Desktop:" -ForegroundColor Gray
    Write-Host "1. اضغط Windows key واكتب 'Docker Desktop'" -ForegroundColor White
    Write-Host "   Press Windows key and type 'Docker Desktop'" -ForegroundColor Gray
    Write-Host "2. اضغط على Docker Desktop لتشغيله" -ForegroundColor White
    Write-Host "   Click on Docker Desktop to start it" -ForegroundColor Gray
    Write-Host "3. انتظر حتى تصبح الأيقونة خضراء" -ForegroundColor White
    Write-Host "   Wait until the icon turns green" -ForegroundColor Gray
    Write-Host "4. ثم شغل هذا الملف مرة أخرى" -ForegroundColor White
    Write-Host "   Then run this file again" -ForegroundColor Gray

    Read-Host "`nاضغط Enter بعد تشغيل Docker Desktop (Press Enter after starting Docker Desktop)"

    # إعادة فحص Docker
    try {
        docker version | Out-Null 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Docker Daemon يعمل الآن" -ForegroundColor Green
        } else {
            Write-Host "✗ Docker Daemon ما زال لا يعمل" -ForegroundColor Red
            exit 1
        }
    } catch {
        Write-Host "✗ Docker Daemon ما زال لا يعمل" -ForegroundColor Red
        exit 1
    }
}

# الخطوة 3: فحص ملفات المشروع
Write-Host "`n📋 الخطوة 3: فحص ملفات المشروع" -ForegroundColor Yellow
Write-Host "Step 3: Checking Project Files" -ForegroundColor Gray

$requiredFiles = @("docker-compose.yml", "requirements.txt", "manage.py", "Dockerfile.production")
$allFilesExist = $true

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file موجود" -ForegroundColor Green
    } else {
        Write-Host "✗ $file مفقود" -ForegroundColor Red
        $allFilesExist = $false
    }
}

if (-not $allFilesExist) {
    Write-Host "بعض الملفات المطلوبة مفقودة" -ForegroundColor Red
    exit 1
}

# فحص ملف .env
if (Test-Path ".env") {
    Write-Host "✓ ملف .env موجود" -ForegroundColor Green
} else {
    Write-Host "⚠ ملف .env مفقود - سيتم إنشاؤه" -ForegroundColor Yellow
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
        Write-Host "✓ تم إنشاء ملف .env من القالب" -ForegroundColor Green
    }
}

# الخطوة 4: فحص البورت
Write-Host "`n📋 الخطوة 4: فحص البورت 8000" -ForegroundColor Yellow
Write-Host "Step 4: Checking Port 8000" -ForegroundColor Gray

try {
    $connection = New-Object System.Net.Sockets.TcpClient("localhost", 8000)
    $connection.Close()
    Write-Host "⚠ البورت 8000 مستخدم" -ForegroundColor Yellow
    Write-Host "⚠ Port 8000 is in use" -ForegroundColor Gray

    $continue = Read-Host "هل تريد المتابعة؟ (y/n) Continue anyway? (y/n)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        exit 1
    }
} catch {
    Write-Host "✓ البورت 8000 متاح" -ForegroundColor Green
    Write-Host "✓ Port 8000 is available" -ForegroundColor Gray
}

# الخطوة 5: تشغيل النظام
Write-Host "`n📋 الخطوة 5: تشغيل النظام" -ForegroundColor Yellow
Write-Host "Step 5: Starting the System" -ForegroundColor Gray

Write-Host "جاري تشغيل النظام..." -ForegroundColor Cyan
Write-Host "Starting the system..." -ForegroundColor Gray

# تحديد نوع Docker Compose المتاح
$useDockerCompose = $false
if (Get-Command docker-compose -ErrorAction SilentlyContinue) {
    $dockerComposeCmd = "docker-compose"
    $useDockerCompose = $true
} else {
    try {
        docker compose version | Out-Null 2>&1
        if ($LASTEXITCODE -eq 0) {
            $dockerComposeCmd = "docker compose"
            $useDockerCompose = $true
        }
    } catch {}
}

if ($useDockerCompose) {
    Write-Host "استخدام: $dockerComposeCmd" -ForegroundColor Cyan
    Write-Host "Using: $dockerComposeCmd" -ForegroundColor Gray

    # إيقاف أي حاويات موجودة
    Write-Host "إيقاف الحاويات الموجودة..." -ForegroundColor Yellow
    Invoke-Expression "$dockerComposeCmd down" 2>$null

    # بناء وتشغيل الحاويات
    Write-Host "بناء وتشغيل الحاويات..." -ForegroundColor Yellow
    Write-Host "Building and starting containers..." -ForegroundColor Gray
    Invoke-Expression "$dockerComposeCmd up --build -d"

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ تم تشغيل الحاويات بنجاح" -ForegroundColor Green
    } else {
        Write-Host "✗ فشل في تشغيل الحاويات" -ForegroundColor Red
        Write-Host "جاري المحاولة بطريقة أخرى..." -ForegroundColor Yellow
        $useDockerCompose = $false
    }
}

if (-not $useDockerCompose) {
    Write-Host "استخدام Docker مباشرة..." -ForegroundColor Yellow
    Write-Host "Using Docker directly..." -ForegroundColor Gray

    # إنشاء الشبكة
    docker network create origin_network 2>$null

    # تشغيل قاعدة البيانات
    Write-Host "تشغيل قاعدة البيانات..." -ForegroundColor Cyan
    docker run -d --name origin_db --network origin_network -e POSTGRES_DB=origin_certificate -e POSTGRES_USER=django_user -e POSTGRES_PASSWORD=django_password_2024 -p 5432:5432 postgres:15 2>$null

    # بناء التطبيق
    Write-Host "بناء التطبيق..." -ForegroundColor Cyan
    docker build -f Dockerfile.production -t origin-web . 2>$null

    # تشغيل التطبيق
    Write-Host "تشغيل التطبيق..." -ForegroundColor Cyan
    docker run -d --name origin_web --network origin_network -e DATABASE_URL=************************************************************/origin_certificate -p 8000:8000 origin-web 2>$null
}

# الخطوة 6: انتظار التشغيل
Write-Host "`n📋 الخطوة 6: انتظار التشغيل" -ForegroundColor Yellow
Write-Host "Step 6: Waiting for Startup" -ForegroundColor Gray

Write-Host "انتظار بدء الخدمات..." -ForegroundColor Cyan
Write-Host "Waiting for services to start..." -ForegroundColor Gray

for ($i = 1; $i -le 30; $i++) {
    Write-Host "." -NoNewline -ForegroundColor Gray
    Start-Sleep -Seconds 2
}
Write-Host ""

# تشغيل الترحيلات
Write-Host "تشغيل ترحيلات قاعدة البيانات..." -ForegroundColor Cyan
Write-Host "Running database migrations..." -ForegroundColor Gray

if ($useDockerCompose) {
    Invoke-Expression "$dockerComposeCmd exec web python manage.py migrate" 2>$null
} else {
    docker exec origin_web python manage.py migrate 2>$null
}

# الخطوة 7: فحص النتيجة
Write-Host "`n📋 الخطوة 7: فحص النتيجة" -ForegroundColor Yellow
Write-Host "Step 7: Checking Results" -ForegroundColor Gray

Write-Host "فحص حالة الحاويات..." -ForegroundColor Cyan
Write-Host "Checking container status..." -ForegroundColor Gray
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# اختبار الوصول للتطبيق
Write-Host "`nاختبار الوصول للتطبيق..." -ForegroundColor Cyan
Write-Host "Testing application access..." -ForegroundColor Gray

try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000" -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "🎉 النظام يعمل بنجاح!" -ForegroundColor Green
        Write-Host "🎉 System is running successfully!" -ForegroundColor Gray
    }
} catch {
    Write-Host "⚠ التطبيق قد يحتاج وقت إضافي للبدء" -ForegroundColor Yellow
    Write-Host "⚠ Application might need more time to start" -ForegroundColor Gray
}

# النتيجة النهائية
Write-Host "`n" + "=" * 60 -ForegroundColor Cyan
Write-Host "🎯 النتيجة النهائية / Final Result" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Cyan

Write-Host "`n🌐 روابط الوصول / Access Links:" -ForegroundColor Cyan
Write-Host "التطبيق الرئيسي / Main Application: http://localhost:8000" -ForegroundColor White
Write-Host "لوحة الإدارة / Admin Panel: http://localhost:8000/admin" -ForegroundColor White

Write-Host "`n🛠️ أوامر مفيدة / Useful Commands:" -ForegroundColor Cyan
Write-Host "عرض السجلات / View Logs: docker logs origin_web -f" -ForegroundColor White
Write-Host "إنشاء المستخدمين / Create Users: .\create-users.ps1" -ForegroundColor White
Write-Host "إيقاف النظام / Stop System: docker stop origin_web origin_db" -ForegroundColor White

Write-Host "`n🔐 إنشاء المستخدمين الافتراضيين:" -ForegroundColor Cyan
Write-Host "Creating default users..." -ForegroundColor Gray
$createUsers = Read-Host "هل تريد إنشاء المستخدمين الافتراضيين الآن؟ (y/n) Create default users now? (y/n)"

if ($createUsers -eq "y" -or $createUsers -eq "Y") {
    Write-Host "جاري إنشاء المستخدمين..." -ForegroundColor Yellow
    try {
        .\create-users.ps1
    } catch {
        Write-Host "يمكنك إنشاء المستخدمين لاحقاً باستخدام: .\create-users.ps1" -ForegroundColor Yellow
        Write-Host "You can create users later using: .\create-users.ps1" -ForegroundColor Gray
    }
} else {
    Write-Host "يمكنك إنشاء المستخدمين لاحقاً باستخدام: .\create-users.ps1" -ForegroundColor Yellow
    Write-Host "You can create users later using: .\create-users.ps1" -ForegroundColor Gray
}

Write-Host "`nاضغط Enter للخروج / Press Enter to exit..." -ForegroundColor Gray
Read-Host
