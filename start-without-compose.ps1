# Start Origin Certificate System without Docker Compose
# This script uses pure Docker commands

Write-Host "=== Starting Origin Certificate System (Docker Only) ===" -ForegroundColor Green

# Check if Docker is available
if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Host "Error: Docker is not installed" -ForegroundColor Red
    exit 1
}

# Check if Docker daemon is running
Write-Host "Checking Docker daemon..." -ForegroundColor Yellow
try {
    docker version | Out-Null
    Write-Host "✓ Docker daemon is running" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker daemon is not running" -ForegroundColor Red
    Write-Host "Please start Docker Desktop manually:" -ForegroundColor Yellow
    Write-Host "1. Press Windows key and search for 'Docker Desktop'" -ForegroundColor White
    Write-Host "2. Click on Docker Desktop to start it" -ForegroundColor White
    Write-Host "3. Wait for the Docker icon to appear in system tray" -ForegroundColor White
    Write-Host "4. Run this script again" -ForegroundColor White
    exit 1
}

# Create Docker network
Write-Host "Creating Docker network..." -ForegroundColor Yellow
docker network create origin_network 2>$null
Write-Host "✓ Network ready" -ForegroundColor Green

# Start PostgreSQL database
Write-Host "Starting PostgreSQL database..." -ForegroundColor Yellow
docker run -d `
    --name origin_db `
    --network origin_network `
    -e POSTGRES_DB=origin_certificate `
    -e POSTGRES_USER=django_user `
    -e POSTGRES_PASSWORD=django_password_2024 `
    -p 5432:5432 `
    postgres:15

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Database container started" -ForegroundColor Green
} else {
    Write-Host "⚠ Database container might already exist, continuing..." -ForegroundColor Yellow
}

# Wait for database to be ready
Write-Host "Waiting for database to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# Build the web application
Write-Host "Building web application..." -ForegroundColor Yellow
docker build -f Dockerfile.production -t origin-certificate-web .

if ($LASTEXITCODE -ne 0) {
    Write-Host "✗ Failed to build web application" -ForegroundColor Red
    exit 1
}

Write-Host "✓ Web application built successfully" -ForegroundColor Green

# Start web application
Write-Host "Starting web application..." -ForegroundColor Yellow
docker run -d `
    --name origin_web `
    --network origin_network `
    -e DATABASE_URL=************************************************************/origin_certificate `
    -e DEBUG=False `
    -e ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0 `
    -p 8000:8000 `
    origin-certificate-web

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Web application started" -ForegroundColor Green
} else {
    Write-Host "⚠ Web container might already exist, continuing..." -ForegroundColor Yellow
}

# Wait for application to start
Write-Host "Waiting for application to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 20

# Run database migrations
Write-Host "Running database migrations..." -ForegroundColor Yellow
docker exec origin_web python manage.py migrate

# Check if application is running
Write-Host "Checking application status..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000" -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "🎉 Application is running successfully!" -ForegroundColor Green
        Write-Host "Access the application at: http://localhost:8000" -ForegroundColor Cyan
        Write-Host "Admin panel: http://localhost:8000/admin" -ForegroundColor Cyan
    }
} catch {
    Write-Host "⚠ Application might still be starting..." -ForegroundColor Yellow
    Write-Host "Try accessing http://localhost:8000 in a few minutes" -ForegroundColor White
}

Write-Host "`n=== Container Status ===" -ForegroundColor Cyan
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

Write-Host "`n=== Useful Commands ===" -ForegroundColor Cyan
Write-Host "View web logs: docker logs origin_web -f" -ForegroundColor White
Write-Host "View db logs: docker logs origin_db -f" -ForegroundColor White
Write-Host "Create superuser: docker exec -it origin_web python manage.py createsuperuser" -ForegroundColor White
Write-Host "Stop containers: docker stop origin_web origin_db" -ForegroundColor White
Write-Host "Remove containers: docker rm origin_web origin_db" -ForegroundColor White

Write-Host "`nPress Enter to view application logs..." -ForegroundColor Yellow
Read-Host
docker logs origin_web -f
