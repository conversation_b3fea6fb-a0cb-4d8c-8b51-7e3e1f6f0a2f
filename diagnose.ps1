# Quick diagnosis script for Origin Certificate System issues

Write-Host "=== Origin Certificate System - Quick Diagnosis ===" -ForegroundColor Green

# Function to check command availability
function Test-Command {
    param([string]$Command)
    return (Get-Command $Command -ErrorAction SilentlyContinue) -ne $null
}

# Function to test port
function Test-Port {
    param([string]$Host, [int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient($Host, $Port)
        $connection.Close()
        return $true
    } catch {
        return $false
    }
}

Write-Host "`n=== System Information ===" -ForegroundColor Yellow
Write-Host "OS: $((Get-WmiObject Win32_OperatingSystem).Caption)"
Write-Host "PowerShell: $($PSVersionTable.PSVersion)"
Write-Host "Current User: $env:USERNAME"
Write-Host "Is Admin: $(([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] 'Administrator'))"

Write-Host "`n=== Docker Status ===" -ForegroundColor Yellow

# Check Docker installation
if (Test-Command "docker") {
    Write-Host "✓ Docker command available" -ForegroundColor Green
    try {
        $dockerVersion = docker --version
        Write-Host "✓ Docker version: $dockerVersion" -ForegroundColor Green
    } catch {
        Write-Host "✗ Docker command failed" -ForegroundColor Red
    }
} else {
    Write-Host "✗ Docker command not found" -ForegroundColor Red
    Write-Host "  Solution: Run .\install-docker-simple.ps1" -ForegroundColor Cyan
}

# Check Docker daemon
if (Test-Command "docker") {
    try {
        docker version | Out-Null
        Write-Host "✓ Docker daemon is running" -ForegroundColor Green
    } catch {
        Write-Host "✗ Docker daemon is not running" -ForegroundColor Red
        Write-Host "  Solution: Start Docker Desktop" -ForegroundColor Cyan
    }
}

# Check Docker Compose
if (Test-Command "docker-compose") {
    Write-Host "✓ docker-compose available" -ForegroundColor Green
} elseif (Test-Command "docker") {
    try {
        docker compose version | Out-Null
        Write-Host "✓ docker compose available" -ForegroundColor Green
    } catch {
        Write-Host "⚠ docker compose not available" -ForegroundColor Yellow
    }
} else {
    Write-Host "✗ Docker Compose not available" -ForegroundColor Red
}

Write-Host "`n=== Project Files ===" -ForegroundColor Yellow

# Check essential files
$essentialFiles = @(
    "docker-compose.yml",
    "Dockerfile.production", 
    "requirements.txt",
    "manage.py",
    ".env"
)

foreach ($file in $essentialFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file exists" -ForegroundColor Green
    } else {
        Write-Host "✗ $file missing" -ForegroundColor Red
        if ($file -eq ".env") {
            Write-Host "  Solution: Copy-Item .env.example .env" -ForegroundColor Cyan
        }
    }
}

Write-Host "`n=== Port Status ===" -ForegroundColor Yellow

# Check if ports are available
$ports = @(8000, 5432, 80)
foreach ($port in $ports) {
    if (Test-Port "localhost" $port) {
        Write-Host "⚠ Port $port is in use" -ForegroundColor Yellow
        # Try to find what's using the port
        try {
            $process = netstat -ano | Select-String ":$port " | Select-Object -First 1
            if ($process) {
                Write-Host "  Used by: $process" -ForegroundColor Gray
            }
        } catch {}
    } else {
        Write-Host "✓ Port $port is available" -ForegroundColor Green
    }
}

Write-Host "`n=== Container Status ===" -ForegroundColor Yellow

if (Test-Command "docker") {
    try {
        # Check if containers exist
        $containers = docker ps -a --format "table {{.Names}}\t{{.Status}}" 2>$null
        if ($containers) {
            Write-Host "Containers found:" -ForegroundColor Cyan
            Write-Host $containers
        } else {
            Write-Host "No containers found" -ForegroundColor Gray
        }
        
        # Check running containers
        $runningContainers = docker ps --format "{{.Names}}" 2>$null
        if ($runningContainers) {
            Write-Host "`nRunning containers:" -ForegroundColor Green
            $runningContainers | ForEach-Object { Write-Host "  ✓ $_" -ForegroundColor Green }
        } else {
            Write-Host "No containers currently running" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "Could not check container status" -ForegroundColor Red
    }
}

Write-Host "`n=== Network Connectivity ===" -ForegroundColor Yellow

# Test internet connectivity
try {
    Test-NetConnection google.com -Port 80 -InformationLevel Quiet | Out-Null
    Write-Host "✓ Internet connectivity available" -ForegroundColor Green
} catch {
    Write-Host "✗ Internet connectivity issues" -ForegroundColor Red
}

# Test localhost connectivity
if (Test-Port "localhost" 8000) {
    Write-Host "✓ Application appears to be running on port 8000" -ForegroundColor Green
} else {
    Write-Host "✗ No application running on port 8000" -ForegroundColor Yellow
}

Write-Host "`n=== Disk Space ===" -ForegroundColor Yellow

$disk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
$freeSpaceGB = [math]::Round($disk.FreeSpace / 1GB, 2)
$totalSpaceGB = [math]::Round($disk.Size / 1GB, 2)

Write-Host "C: Drive - Free: $freeSpaceGB GB / Total: $totalSpaceGB GB"

if ($freeSpaceGB -lt 5) {
    Write-Host "⚠ Low disk space! Consider cleaning up." -ForegroundColor Red
} elseif ($freeSpaceGB -lt 20) {
    Write-Host "⚠ Disk space getting low" -ForegroundColor Yellow
} else {
    Write-Host "✓ Sufficient disk space" -ForegroundColor Green
}

Write-Host "`n=== Recommendations ===" -ForegroundColor Cyan

$issues = @()

if (!(Test-Command "docker")) {
    $issues += "Install Docker: .\install-docker-simple.ps1"
}

if (!(Test-Path ".env")) {
    $issues += "Create .env file: Copy-Item .env.example .env"
}

if (Test-Port "localhost" 8000) {
    $issues += "Port 8000 is busy. Stop other services or change port."
}

if ($freeSpaceGB -lt 10) {
    $issues += "Free up disk space (current: $freeSpaceGB GB)"
}

if ($issues.Count -eq 0) {
    Write-Host "🎉 No major issues detected!" -ForegroundColor Green
    Write-Host "Try running: .\start-docker.ps1" -ForegroundColor White
} else {
    Write-Host "Issues to address:" -ForegroundColor Yellow
    $issues | ForEach-Object { Write-Host "  • $_" -ForegroundColor White }
}

Write-Host "`n=== Quick Actions ===" -ForegroundColor Cyan
Write-Host "Check requirements: .\check-requirements.ps1" -ForegroundColor White
Write-Host "Install Docker: .\install-docker-simple.ps1" -ForegroundColor White
Write-Host "Start system: .\start-docker.ps1" -ForegroundColor White
Write-Host "Test system: .\test-system.ps1" -ForegroundColor White
Write-Host "View troubleshooting: Get-Content TROUBLESHOOTING.md" -ForegroundColor White
