# 🔐 معلومات تسجيل الدخول - نظام شهادات المنشأ

## 👑 المدير العام (Super Admin)

| المعلومة | القيمة |
|---------|--------|
| **اسم المستخدم** | `super_admin` |
| **كلمة المرور** | `securepassword123` |
| **الصلاحيات** | جميع الصلاحيات - إدارة كاملة للنظام |
| **الوصول** | جميع الفروع والوظائف |

---

## 🏢 مديري الفروع (Branch Admins)

### 📍 محطة الرمل
| المعلومة | القيمة |
|---------|--------|
| **اسم المستخدم** | `محطة_الرمل_admin` |
| **كلمة المرور** | `securepassword123` |
| **الفرع** | محطة الرمل |
| **الصلاحيات** | إدارة فرع محطة الرمل |

### 📍 السلطان حسين
| المعلومة | القيمة |
|---------|--------|
| **اسم المستخدم** | `السلطان_حسين_admin` |
| **كلمة المرور** | `securepassword123` |
| **الفرع** | السلطان حسين |
| **الصلاحيات** | إدارة فرع السلطان حسين |

### 📍 الاستثماري
| المعلومة | القيمة |
|---------|--------|
| **اسم المستخدم** | `الاستثماري_admin` |
| **كلمة المرور** | `securepassword123` |
| **الفرع** | الاستثماري |
| **الصلاحيات** | إدارة فرع الاستثماري |

---

## 👥 مستخدمي الفروع (Branch Users)

### 📍 محطة الرمل
| المعلومة | القيمة |
|---------|--------|
| **اسم المستخدم** | `محطة_الرمل_user` |
| **كلمة المرور** | `securepassword123` |
| **الفرع** | محطة الرمل |
| **الصلاحيات** | إضافة وتعديل الشهادات في فرع محطة الرمل |

### 📍 السلطان حسين
| المعلومة | القيمة |
|---------|--------|
| **اسم المستخدم** | `السلطان_حسين_user` |
| **كلمة المرور** | `securepassword123` |
| **الفرع** | السلطان حسين |
| **الصلاحيات** | إضافة وتعديل الشهادات في فرع السلطان حسين |

### 📍 الاستثماري
| المعلومة | القيمة |
|---------|--------|
| **اسم المستخدم** | `الاستثماري_user` |
| **كلمة المرور** | `securepassword123` |
| **الفرع** | الاستثماري |
| **الصلاحيات** | إضافة وتعديل الشهادات في فرع الاستثماري |

---

## 🌐 روابط الوصول

- **التطبيق الرئيسي:** http://localhost:8000
- **لوحة الإدارة:** http://localhost:8000/admin
- **صفحة تسجيل الدخول:** http://localhost:8000/login

---

## 🔑 ملاحظات أمنية مهمة

### ⚠️ تحذيرات أمنية:
1. **غير كلمات المرور** فور التشغيل الأول في بيئة الإنتاج
2. **استخدم كلمات مرور قوية** تحتوي على أرقام ورموز
3. **لا تشارك معلومات تسجيل الدخول** مع أشخاص غير مخولين
4. **راجع صلاحيات المستخدمين** بانتظام

### 🔒 توصيات كلمات المرور الآمنة:
- **الطول:** 12 حرف على الأقل
- **التنوع:** أحرف كبيرة وصغيرة وأرقام ورموز
- **التفرد:** كلمة مرور مختلفة لكل مستخدم
- **التحديث:** تغيير كلمات المرور كل 3-6 أشهر

---

## 🛠️ إنشاء المستخدمين

لإنشاء هؤلاء المستخدمين تلقائياً، شغل:

```powershell
.\create-users.ps1
```

أو يدوياً:

```powershell
# الدخول لحاوية Django
docker exec -it origin_web python manage.py shell

# أو باستخدام docker-compose
docker-compose exec web python manage.py shell
```

---

## 📊 مستويات الصلاحيات

### 👑 المدير العام (Super Admin)
- ✅ إنشاء وحذف المستخدمين
- ✅ إدارة جميع الفروع
- ✅ عرض جميع الشهادات
- ✅ إنشاء وتعديل الشركات والبضائع
- ✅ إنشاء التقارير الشاملة
- ✅ إعدادات النظام

### 🏢 مدير الفرع (Branch Admin)
- ✅ إدارة مستخدمي فرعه
- ✅ عرض وتعديل شهادات فرعه
- ✅ إنشاء التقارير لفرعه
- ❌ الوصول لفروع أخرى

### 👤 مستخدم الفرع (Branch User)
- ✅ إضافة شهادات جديدة لفرعه
- ✅ تعديل الشهادات التي أنشأها
- ✅ عرض شهادات فرعه
- ❌ حذف الشهادات
- ❌ إدارة المستخدمين

---

## 🔄 تغيير كلمة المرور

### من لوحة الإدارة:
1. سجل دخول كمدير عام
2. اذهب إلى "Users"
3. اختر المستخدم
4. اضغط "Change password"

### من سطر الأوامر:
```powershell
docker exec -it origin_web python manage.py changepassword اسم_المستخدم
```
