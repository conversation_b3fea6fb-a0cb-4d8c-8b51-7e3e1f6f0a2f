/* General Styles */
@font-face {
  font-family: 'YaModernPro-Bold';
  src: url('fonts/alfont_com_Ya-ModernPro-Bold.otf') format('opentype'),
       url('fonts/alfont_com_Ya-ModernPro-Bold.woff2') format('woff2'),
       url('fonts/alfont_com_Ya-ModernPro-Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}
* {
    font-family: 'Tajawal', sans-serif;
}

html, body {
  /* font-family: 'Cairo', sans-serif; */
  direction: rtl; /* Right-to-left for Arabic */
  text-align: right;
  background-color: #f0f4f8;
  margin: 0;
  padding: 0;
  color: #333;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Navigation Bar */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #2c3e50, #3498db);
  color: white;
  padding: 0 20px;
  width: 100%;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 1000;
  box-sizing: border-box;
  height: 70px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  direction: rtl;
  font-family: 'Tajawal', sans-serif;
}

.navbar-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
  text-align: right;
  padding-left: 15px;
  direction: ltr;
}

.navbar-left {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex: 1;
  text-align: left;
  padding-right: 15px;
  direction: ltr;
}

.logo {
  height: 50px;
  width: auto;
  margin: 0 15px;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  transition: transform 0.3s ease;
}

.logo:hover {
  transform: scale(1.05);
}

/* Header text styles */
#headerRight, #headerLeft {
  font-size: clamp(14px, 1.5vw, 20px);
  font-weight: 700;
  margin: 0 8px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
  transition: color 0.3s ease;
}

#headerRight2, #headerLeft2 {
  font-size: clamp(12px, 1.2vw, 16px);
  font-weight: 600;
  margin: 0 8px;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.navbar-right:hover #headerRight,
.navbar-left:hover #headerLeft {
  color: #f8f9fa;
}

.navbar-right:hover #headerRight2,
.navbar-left:hover #headerLeft2 {
  opacity: 1;
}

/* Menu Button */
.menu-btn {
  font-size: 22px;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  min-width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  order: 1;
  margin-left: 10px;
}

.menu-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Navbar Controls Container */
.navbar-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .navbar {
    padding: 0 15px;
  }
  
  #headerRight, #headerLeft {
    font-size: clamp(13px, 1.3vw, 18px);
  }
  
  #headerRight2, #headerLeft2 {
    font-size: clamp(11px, 1.1vw, 14px);
  }
  
  .logo {
    height: 45px;
    margin: 0 10px;
  }
}

@media (max-width: 768px) {
  .navbar {
    flex-wrap: wrap;
    height: auto;
    padding: 10px;
  }
  
  .navbar-right, .navbar-left {
    flex: 100%;
    justify-content: center;
    padding: 5px 0;
  }
  
  #headerRight, #headerLeft,
  #headerRight2, #headerLeft2 {
    font-size: 13px;
    text-align: center;
  }
  
  .logo {
    height: 40px;
    margin: 0 8px;
  }
  
  .navbar-controls {
    order: -1;
    width: 100%;
    justify-content: space-between;
    margin-bottom: 10px;
  }
  
  .menu-btn, .user-btn {
    margin: 0;
  }
}

@media (max-width: 480px) {
  #headerRight, #headerLeft {
    display: none;
  }
  
  #headerRight2, #headerLeft2 {
    font-size: 12px;
  }
  
  .logo {
    height: 36px;
  }
}

.centered-title {
  font-family: 'YaModernPro-Bold', 'Tajawal', sans-serif;
  font-size: 2.5rem;
  font-weight: 700;  font-style: italic;
  color: #13679f;
  text-align: center;
  direction: rtl;
  margin: 40px 0;
  position: relative;
  perspective: 500px;  
  /* Gradient Text Color */
  background: linear-gradient(135deg, #13679f 0%, #1a8fd3 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  
  /* Border effect */
  padding: 0 15px;
  transform: rotateX(5deg) rotateY(-5deg);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.centered-title::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.3) 0%, transparent 50%);
  transform: translateZ(-10px);
  z-index: -1;
  border-radius: 5px;
  filter: blur(2px);
}
/* Optional: Add decorative elements */
.centered-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 25%;
  width: 50%;
  height: 3px;
  background: linear-gradient(90deg, transparent, #f39c12, transparent);
  transform: perspective(100px) rotateX(45deg);
  opacity: 0.8;
  transition: all 0.4s ease;
}

.centered-title:hover::after {
  width: 60%;
  background: linear-gradient(90deg, transparent, #f39c12, #e74c3c, transparent);
}

.centered-title .title-icon {
  color: #f39c12 ; /* Gold color for seal */
  text-shadow: 0 0 5px rgba(212, 175, 55, 0.3);
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}
/* Sidebar Styles */
/* Corrected Sidebar Styles with Perfect Alignment */
.sidebar {
  position: fixed;
  top: 0;
  left: -250px;
  width: 250px;
  height: 100%;
  background: linear-gradient(135deg, #2c3e50, #3498db);
  color: white;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  padding-top: 70px;
  margin-top: 70px;
  z-index: 999;
  box-shadow: 5px 0 15px rgba(0, 0, 0, 0.1);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar.active {
  left: 0;
}

/* Close Button */
.sidebar a:first-child {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 24px;
  color: white;
  background: rgba(255, 255, 255, 0.2);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

/* Menu Items Container */
.sidebar-menu {
  display: flex;
  flex-direction: column;
  padding: 10px 0;
}

/* Individual Menu Items */
.sidebar a:not(:first-child) {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  margin: 0 10px;
  text-decoration: none;
  color: white;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 4px;
  direction: rtl;
  text-align: right;
}

/* Icon Styling */
.sidebar a:not(:first-child) i {
  width: 24px;
  text-align: center;
  margin-left: 10px;
  font-size: 18px;
  flex-shrink: 0;
}

/* Hover Effects */
.sidebar a:not(:first-child):hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-right: 3px solid #f39c12;
  transform: translateX(-5px);
}

/* Active Item Indicator */
.sidebar a:not(:first-child).active {
  background-color: rgba(255, 255, 255, 0.15);
  border-right: 3px solid #f39c12;
  font-weight: 600;
}

/* Animation Line Effect */
.sidebar a:not(:first-child)::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: width 0.4s ease;
}

.sidebar a:not(:first-child):hover::after {
  width: 100%;
}

/* Container */
.container {
  max-width: 1400px; 
  margin: 90px auto; 
  padding: 10px; 
  background-color: #ffffff;
  border-radius: 20px; 
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); 
}

.content-wrapper {
  flex: 1;
}

/* Heading Styling */
h1 {
  text-align: center;
  color: #4d79b7;
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  padding-bottom: 5px;
}

/* Filter Section */
.filter {
  flex: 1;
  padding: 10px; 
  border-radius: 10px; 
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3); 
  margin-top: 10px;
}

/* Form Row */
.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  flex-wrap: wrap;
  justify-content: space-between;
}

/* Form Containers */
.form-container {
  flex: 1;
  padding: 10px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  min-width: 300px;
}

/* Center Container */
.CenterContainer {
  /* width: 100%; */
  padding: 10px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  margin-top: 15px;
}

/* Form Group */
.form-group {
  margin-bottom: 10px;
  /* display: flex; */
  flex-direction: column;
  align-items: flex-start;
}

.form-group label {
  width: 100%;
  margin-bottom: 3px;
  font-weight: bold;
  color: #1b2e49;
  font-size: 12px;
  text-align: right;
}

/* Input and Select Fields */
.form-group input,
.form-group select {
  width: 100%;
  padding: 8px;
  border: 2px solid #ced4da;
  border-radius: 6px;
  font-size: 10px;
  transition: border-color 0.3s ease;
  text-align: right;
  box-sizing: border-box;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus {
  border-color: #1b2e49;
  outline: none;
}

/* Buttons */ 
button {
  margin-top: 5px;
  padding: 8px 16px;
  background-color: #13679f;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #aec4e3;
}

/* Table Styling */
.table-container {
  display: block;
  max-width: 100%;
  max-height: 390px; /* ~350px for 10 rows + ~40px for header */
  overflow-x: auto; /* Horizontal scrolling for wide tables */
  overflow-y: auto; /* Vertical scrolling for more than 10 rows */
  margin-top: 15px;
  position: relative;
  z-index: 1;
}
.table-container th {
  background-color: #2d78ab;
}
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

table th,
table td {
  padding: 8px;
  border: 1px solid #dee2e6;
  text-align: center;
  font-size: 12px;
}

table th {
  background-color: #2d78ab;
  color: white;
  font-size: 14px;
  white-space: nowrap;
  position: sticky;
  top: 0;
  z-index: 2; /* Above table rows */
}

/* table tr:nth-child(even) {
  background-color: #f2f2f2;
} */

table tr:hover {
  background-color: #e0e0e0;
}

.date-column {
  white-space: nowrap;
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}
#modal {
  z-index: 1002; /* Ensure it’s above editModal */
}

.modal-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
}

.modal-content label {
  font-weight: 600;
  color: #333;
}

.modal-content input {
  padding: 10px;
  border: 2px solid #6c757d;
  border-radius: 6px;
}

/* Close Button */
.close {
  cursor: pointer;
  font-size: 20px;
  text-align: right;
}

/* Save Button */
.save-button-container {
  text-align: center;
  margin-top: 15px;
}

#saveButton {
  padding: 10px 20px;
  font-size: 16px;
  transition: background-color 0.3s;
}

#saveButton:hover {
  background-color: #9db4da;
}

#newTableContainer {
  width: 100%;
  overflow-x: auto;
  margin-top: 20px;
  display: block;
}

/* New Table Container */
#newTableContainer table {
  width: 100%;
  min-width: 900px;
  border-collapse: collapse;
  margin-top: 10px;
  overflow-x: auto;
}

#newTableContainer th, 
#newTableContainer td {
  border: 1px solid #6b6969;
  padding: 6px;
  text-align: center;
}

#newTableContainer th {
  background-color: #3190d0;
}

/* Form Group with Add Button */
.form-group.with-add-button {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Add Button */
.add-button {
  padding: 8px 10px;
  font-size: 12px;
  transition: background-color 0.3s ease;
  margin-top: 5px;
}

.add-button:hover {
  background-color: #0f1c2e;
}

::placeholder {
  color: #000000;
  font-size: 10px;
}

#editModal {
  width: 90%; /* Responsive width */
  max-width: 1000px; /* Maximum width */
  min-width: 300px; /* Minimum width */
  max-height: 90vh; /* Maximum height relative to viewport */
  padding: 20px;
  box-sizing: border-box;
  overflow-y: auto; /* Enable vertical scrolling */
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 1001; /* Higher than other elements */
}

#editForm {
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;
}

.filter-options {
  margin-bottom: 20px;
}

.filter-options label {
  margin-right: 10px;
}

.report-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.report-table th, .report-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: center;
}

.report-table th {
  background-color: #f4f4f4;
}

.download-buttons {
  margin-top: 20px;
}

.download-buttons button {
  margin-right: 10px;
}

a {
  color: white;
  text-decoration: none;
}

.footer {
  direction: ltr; /* Force left-to-right */
  text-align: left; /* Align text to the left */
  /* Keep your existing styles */
  width: 96%;
  background: linear-gradient(90deg, #2C3E50, #3498DB);
  color: white;
  font-size: 15px;
  padding: 2%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* If you have specific elements inside the footer that need adjustment */
.footer p {
  direction: ltr; /* Ensure text direction is LTR */
  text-align: left; /* Explicit left alignment */
  margin: 0;
  flex: 1;
}

.footer-img {
  /* If your image needs to be on the right side */
  order: 1; /* This will move it to the right in flex container */
  height: 80%;
  margin-left: 10px;
  margin-right: 10px;
}

/* Match your existing .form-group input/select styling */
.select2-container--default .select2-selection--single {
  width: 100% !important;
  height: auto !important;
  padding: 8px !important;
  border: 2px solid #ced4da !important;
  border-radius: 6px !important;
  font-size: 10px !important;
  text-align: right !important;
  box-sizing: border-box !important;
  font-family: inherit !important;
  background: #fff !important;
}

/* Remove extra padding in the rendered text area and keep line-height normal */
.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: normal !important;
  padding: 0 !important;
  margin-right: 2px !important;
  color: #333;
}

/* Position the dropdown arrow for RTL */
.select2-container--default .select2-selection--single .select2-selection__arrow {
  top: 50% !important;
  transform: translateY(-50%) !important;
  left: 8px !important;
  right: auto !important;
  background: none !important;
}

/* Adjust arrow triangle */
.select2-container--default .select2-selection--single .select2-selection__arrow b {
  border-color: #333 transparent transparent transparent !important;
}

/* Style the dropdown itself */
.select2-container--default .select2-dropdown {
  border: 2px solid #ced4da !important;
  border-radius: 6px !important;
}

/* The small search box at the top of the dropdown */
.select2-container--default .select2-search--dropdown {
  padding: 5px !important;
  background-color: #f8f9fa;
  border-bottom: 1px solid #ced4da;
}

/* Search field inside the dropdown */
.select2-container--default .select2-search--dropdown .select2-search__field {
  width: 100% !important;
  padding: 8px !important;
  font-size: 10px !important;
  border: none !important;
  background-color: transparent !important;
  text-align: right !important;
  direction: rtl !important;
  box-sizing: border-box;
}

/* The list of options */
.select2-container--default .select2-results__options {
  max-height: 250px;
  overflow-y: auto;
  font-size: 10px !important;
  text-align: right !important;
  direction: rtl !important;
}

/* Highlighted option on hover/keyboard nav */
.select2-container--default .select2-results__option--highlighted {
  background-color: #aec4e3 !important;
  color: #1b2e49 !important;
}

/* For single-select placeholders */
.select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: #000 !important;
}

/* For the search field placeholder inside the dropdown */
.select2-container--default .select2-search--dropdown .select2-search__field::placeholder {
  color: #000 !important;
}

/* Shipment section header */
.shipment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

/* Inline grouping for cargo items */
.inline-group {
  display: flex;
  flex-direction: row;
  gap: 15px;
  align-items: flex-end;
  margin-bottom: 10px;
}

.inline-group > div {
  display: flex;
  flex-direction: column;
  flex: 1;
}

/* Styling for each shipment group */
.shipment {
  border: 1px solid #ccc;
  padding: 10px;
  margin-bottom: 15px;
}

.shipment-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin-top: 20px;
}

.required-asterisk {
  color: red;
  margin-left: 4px; 
  font-weight: bold;
}

html[dir="rtl"] .required-asterisk {
  margin-right: 0;
  margin-left: 4px;
}

/* User Dropdown Styles */
/* Creative User Dropdown Redesign */
.user-dropdown {
  position: relative;
  display: inline-block;
  margin-left: 15px;
  z-index: 1001;
}

.user-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 8px 15px;
  border-radius: 30px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.user-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.user-icon {
  font-size: 1.2rem;
  margin-left: 8px;
}

.dropdown-content {
  display: none;
  position: absolute;
  left: 0;
  top: calc(100% + 10px);
  background: white;
  min-width: 280px;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  transform-origin: top left;
  transform: scale(0.9);
  opacity: 0;
  transition: all 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.175);
  direction: rtl;
  text-align: right;
  z-index: 1000;
}

.show-dropdown {
  display: block;
  transform: scale(1);
  opacity: 1;
}

/* User Info Section */
.user-info {
  padding: 20px;
  background: linear-gradient(135deg, #3498db, #2c3e50);
  color: white;
  position: relative;
  overflow: hidden;
}

.user-info::before {
  content: "";
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  z-index: 0;
}

.user-info p {
  margin: 8px 0;
  position: relative;
  z-index: 1;
}

.user-info p strong {
  font-weight: 600;
}

/* Dropdown Links */
.dropdown-content a {
  color: #333;
  padding: 12px 20px;
  text-decoration: none;
  display: block;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  overflow: hidden;
}

.dropdown-content a:last-child {
  border-bottom: none;
}

.dropdown-content a::before {
  content: "";
  position: absolute;
  top: 0;
  right: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.1), transparent);
  transition: all 0.4s ease;
}

.dropdown-content a:hover {
  background-color: #f8f9fa;
  padding-right: 25px;
}

.dropdown-content a:hover::before {
  right: 100%;
}

/* Logout Button */
.logout-btn {
  color: #f39c12 !important;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 12px 20px;
  text-align: center;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  border-top: 1px solid #f0f0f0;
  margin-top: 5px
}

.logout-btn i {
  margin-right: 8px;
  margin-left: 0;
}

/* Dropdown Arrow */
.dropdown-content::after {
  content: "";
  position: absolute;
  bottom: 100%;
  left: 20px;
  border-width: 8px;
  border-style: solid;
  border-color: transparent transparent white transparent;
}

/* Animation when dropdown opens */
@keyframes fadeInDropdown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.show-dropdown {
  animation: fadeInDropdown 0.3s ease forwards;
}
/* Centered Alert Styles */
.centered-alert {
  position: fixed;
  top: 10%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px 30px;
  border-radius: 8px;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  opacity: 1;
  transition: opacity 0.5s ease;
  min-width: 300px;
  max-width: 80%;
  text-align: center;
}

.centered-alert.success {
  background-color: #4CAF50;
}

.centered-alert.error {
  background-color: #F44336;
}

.centered-alert .alert-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.centered-alert i {
  font-size: 1.2em;
}

.centered-alert.fade-out {
  opacity: 0;
}

/* Animation for new alerts */
@keyframes slideIn {
  from {
    transform: translate(-50%, -40%);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%);
    opacity: 1;
  }
}

.centered-alert {
  animation: slideIn 0.3s ease-out forwards;
}

/* For the error fields */
.error-field {
  border: 2px solid #F44336 !important;
  animation: shake 0.5s;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  20%, 60% { transform: translateX(-5px); }
  40%, 80% { transform: translateX(5px); }
}

/* Loading spinner */
.fa-spinner.fa-spin {
  margin-left: 8px;
}

/* Style for Select2 dropdowns in shipment groups */
.shipment-group .select2-container {
  width: 100% !important;
  min-width: 100px;
}

.shipment-group .select2-selection {
  height: 38px;
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.shipment-group .select2-selection__arrow {
  height: 36px;
}
.selection{
width: 100%;
}

/* More specific selector for the container */
.form-group .select2-container {
  box-sizing: border-box;
  display: block;
  margin: 0;
  position: relative;
  vertical-align: middle;
  width: 100%;
}

/* More specific selector for the selection */
.form-group .select2-container--default .select2-selection--single {
  width: 100% !important;
  height: auto !important;
  padding: 8px !important;
  border: 2px solid #ced4da !important;
  border-radius: 6px !important;
  font-size: 10px !important;
  text-align: right !important;
  box-sizing: border-box !important;
  font-family: inherit !important;
  background: #fff !important;
}

