# البدء السريع - نظام شهادات المنشأ

## التشغيل السريع (5 دقائق)

### 1. تثبيت Docker (إذا لم يكن مثبتاً)
```powershell
# تشغيل PowerShell كمدير
.\install-docker-windows-server.ps1
# إعادة تشغيل الجهاز بعد التثبيت
```

### 2. إعداد المشروع
```powershell
# نسخ ملف الإعدادات
Copy-Item .env.example .env

# تعديل الإعدادات (اختياري للتجربة)
notepad .env
```

### 3. تشغيل النظام
```powershell
# للإنتاج
.\start-docker.ps1

# أو للتطوير
.\start-dev.ps1
```

### 4. اختبار النظام
```powershell
.\test-system.ps1
```

### 5. الوصول للنظام
- **التطبيق:** http://localhost:8000
- **الإدارة:** http://localhost:8000/admin

## الأوامر الأساسية

### إدارة النظام:
```powershell
# إيقاف النظام
docker-compose down

# إعادة تشغيل
docker-compose restart

# عرض السجلات
docker-compose logs -f

# حالة الخدمات
docker-compose ps
```

### إدارة قاعدة البيانات:
```powershell
# إنشاء مستخدم إداري
docker-compose exec web python manage.py createsuperuser

# تشغيل الترحيلات
docker-compose exec web python manage.py migrate

# نسخة احتياطية
.\docker-maintenance.ps1 -Action backup

# استعادة نسخة احتياطية
.\docker-maintenance.ps1 -Action restore -BackupFile backup_file.sql
```

### التطوير:
```powershell
# فتح Django shell
docker-compose exec web python manage.py shell

# تشغيل الاختبارات
docker-compose exec web python manage.py test

# جمع الملفات الثابتة
docker-compose exec web python manage.py collectstatic
```

## هيكل الملفات المهمة

```
Origin-Certificate/
├── docker-compose.yml          # إعدادات الإنتاج
├── docker-compose.dev.yml      # إعدادات التطوير
├── Dockerfile                  # صورة Docker الأساسية
├── Dockerfile.production       # صورة Docker للإنتاج
├── .env.example               # مثال الإعدادات
├── start-docker.ps1           # تشغيل الإنتاج
├── start-dev.ps1              # تشغيل التطوير
├── docker-maintenance.ps1     # أدوات الصيانة
├── test-system.ps1            # اختبار النظام
└── README.md                  # الدليل الشامل
```

## استكشاف الأخطاء السريع

### المشكلة: Docker لا يعمل
```powershell
# إعادة تشغيل Docker
Restart-Service docker
```

### المشكلة: البورت مستخدم
```powershell
# العثور على العملية
netstat -ano | findstr :8000
# إيقاف العملية
taskkill /PID <process_id> /F
```

### المشكلة: خطأ في قاعدة البيانات
```powershell
# إعادة تشغيل قاعدة البيانات
docker-compose restart db
# فحص السجلات
docker-compose logs db
```

### المشكلة: خطأ في الأذونات
```powershell
# إعادة تعيين الأذونات
docker-compose exec web chown -R app:app /app
```

## الإعدادات المهمة

### للإنتاج (في ملف .env):
```env
DEBUG=False
SECRET_KEY=your-secure-secret-key
ALLOWED_HOSTS=your-domain.com,your-ip
DATABASE_URL=********************************/db
```

### للتطوير:
```env
DEBUG=True
SECRET_KEY=django-insecure-key-for-dev-only
ALLOWED_HOSTS=localhost,127.0.0.1
```

## المساعدة السريعة

- **السجلات:** `docker-compose logs`
- **حالة الخدمات:** `docker-compose ps`
- **إعادة البناء:** `docker-compose up --build`
- **تنظيف:** `docker system prune -f`

للمزيد من التفاصيل، راجع `README.md` أو `INSTALLATION_GUIDE.md`
