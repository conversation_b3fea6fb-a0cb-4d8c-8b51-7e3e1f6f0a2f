# Create superuser for Origin Certificate System
Write-Host "Creating superuser for Origin Certificate System..." -ForegroundColor Green

# Check if we can access Django manage.py
if (Test-Path "manage.py") {
    Write-Host "Found manage.py, creating superuser..." -ForegroundColor Yellow
    
    # Try to create superuser using Django
    try {
        # Set environment variables
        $env:DJANGO_SUPERUSER_USERNAME = "super_admin"
        $env:DJANGO_SUPERUSER_EMAIL = "<EMAIL>"
        $env:DJANGO_SUPERUSER_PASSWORD = "securepassword123"
        
        # Create superuser
        python manage.py createsuperuser --noinput
        
        Write-Host "Superuser created successfully!" -ForegroundColor Green
        Write-Host "Username: super_admin" -ForegroundColor Cyan
        Write-Host "Password: securepassword123" -ForegroundColor Cyan
        
    } catch {
        Write-Host "Error creating superuser: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Try running manually: python manage.py createsuperuser" -ForegroundColor Yellow
    }
} else {
    Write-Host "manage.py not found. Make sure you're in the project directory." -ForegroundColor Red
}

Write-Host "`nTry accessing:" -ForegroundColor Cyan
Write-Host "Admin: http://***************:8010/admin/" -ForegroundColor White
Write-Host "Login: http://***************:8010/login/" -ForegroundColor White
