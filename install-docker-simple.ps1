# Simple Docker installation script for Windows
# Run as Administrator

Write-Host "=== Simple Docker Installation ===" -ForegroundColor Green

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsIn<PERSON><PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "Error: This script must be run as Administrator" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

# Check if Docker is already installed
if (Get-Command docker -ErrorAction SilentlyContinue) {
    Write-Host "Docker is already installed!" -ForegroundColor Green
    docker --version
    Write-Host "You can proceed with running the application." -ForegroundColor Cyan
    exit 0
}

Write-Host "Docker not found. Starting installation..." -ForegroundColor Yellow

# Method 1: Try using Chocolatey (if available)
if (Get-Command choco -ErrorAction SilentlyContinue) {
    Write-Host "Installing Docker using Chocolatey..." -ForegroundColor Yellow
    try {
        choco install docker-desktop -y
        Write-Host "Docker installed successfully via Chocolatey!" -ForegroundColor Green
        Write-Host "Please restart your computer and then start Docker Desktop." -ForegroundColor Yellow
        exit 0
    } catch {
        Write-Host "Chocolatey installation failed. Trying manual method..." -ForegroundColor Yellow
    }
}

# Method 2: Manual download and install
Write-Host "Downloading Docker Desktop..." -ForegroundColor Yellow
$dockerUrl = "https://desktop.docker.com/win/main/amd64/Docker%20Desktop%20Installer.exe"
$dockerInstaller = "$env:TEMP\DockerDesktopInstaller.exe"

try {
    # Download Docker Desktop
    Write-Host "Downloading from: $dockerUrl" -ForegroundColor Cyan
    Invoke-WebRequest -Uri $dockerUrl -OutFile $dockerInstaller -UseBasicParsing
    Write-Host "Download completed!" -ForegroundColor Green
    
    # Install Docker Desktop
    Write-Host "Installing Docker Desktop..." -ForegroundColor Yellow
    Start-Process -FilePath $dockerInstaller -ArgumentList "install --quiet" -Wait
    
    # Clean up
    Remove-Item $dockerInstaller -Force
    
    Write-Host "=== Installation Complete ===" -ForegroundColor Green
    Write-Host "IMPORTANT NEXT STEPS:" -ForegroundColor Yellow
    Write-Host "1. Restart your computer" -ForegroundColor White
    Write-Host "2. Start Docker Desktop from the Start menu" -ForegroundColor White
    Write-Host "3. Wait for Docker to fully start (check system tray)" -ForegroundColor White
    Write-Host "4. Run 'docker --version' to verify" -ForegroundColor White
    Write-Host "5. Then run '.\start-docker.ps1' to start the application" -ForegroundColor White
    
} catch {
    Write-Host "Error during installation: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "`nManual Installation Instructions:" -ForegroundColor Yellow
    Write-Host "1. Go to: https://www.docker.com/products/docker-desktop" -ForegroundColor White
    Write-Host "2. Download Docker Desktop for Windows" -ForegroundColor White
    Write-Host "3. Run the installer as Administrator" -ForegroundColor White
    Write-Host "4. Restart your computer" -ForegroundColor White
    Write-Host "5. Start Docker Desktop" -ForegroundColor White
}

$restart = Read-Host "`nDo you want to restart now? (y/n)"
if ($restart -eq "y" -or $restart -eq "Y") {
    Write-Host "Restarting computer..." -ForegroundColor Yellow
    Restart-Computer -Force
}
