<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>بحث</title>
  {% load static %}
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="{% static 'styles.css' %}">
</head>
<body>

  <nav class="navbar">
    <div class="navbar-right" dir="ltr">
      <!-- <label id="headerRight2">نظام شهادة المصدر والمنشأ | </label> -->
      <label id="headerRight"> الغرفة التجارية المصرية بالاسكندرية </label>
      <img src="{% static 'ACOC_Logo_Final[1].png' %}" alt="Company Logo" class="logo">
    </div>
    <div class="navbar-left" dir="rtl">
      <label id="headerLeft"> Alexandria Chamber of Commerce </label>
      <!-- <label id="headerLeft2"> Origin Certificate System </label> -->
    </div>
    <button class="menu-btn" onclick="toggleSidebar()">☰</button>
<div class="user-dropdown">
  <button class="user-btn" onclick="toggleDropdown()">
    <span class="user-icon">👤</span>
    <span class="user-name">{{ request.user.get_full_name|default:request.user.username|truncatechars:12 }}</span>
  </button>
  <div class="dropdown-content" id="userDropdown">
    <div class="user-info">
      <p><strong>الاسم:</strong> {{ request.user.get_full_name|default:request.user.username }}</p>
      {% if request.user.userprofile.branch %}
        <p><strong>الفرع:</strong> {{ request.user.userprofile.branch.name }}</p>
      {% endif %}
      <p><strong>الدور:</strong> 
        {% if request.user.is_superuser %}
          المشرف العام
        {% elif request.user.userprofile.is_branch_admin %}
          مدير الفرع
        {% else %}
          مستخدم
        {% endif %}
      </p>
    </div>
    <form action="{% url 'logout' %}" method="post" style="display: inline;">
      {% csrf_token %}
      <button type="submit" class="logout-btn">
        <span>تسجيل الخروج</span>
        <i class="fas fa-sign-out-alt"></i>
      </button>
    </form>
  </div>
</div>
</nav>

  <!-- Sidebar -->
<div class="sidebar" id="sidebar">
    <a href="javascript:void(0)" onclick="toggleSidebar()">✖</a>
    <a href="{% url 'index' %}"><i class="fas fa-home"></i> الصفحة الرئيسية</a>
    <a href="{% url 'filter' %}"><i class="fas fa-search"></i> بحث</a>
    <a href="{% url 'cargo' %}"><i class="fas fa-boxes"></i> البضائع</a>
    <a href="{% url 'country' %}"><i class="fas fa-globe"></i> البلد</a>
    <a href="{% url 'report' %}"><i class="fas fa-chart-bar"></i> التقرير</a>
</div>

  <!-- Main Content Wrapper -->
  <div class="content-wrapper">
    <!-- Page Content -->
    <div class="container">
      <!-- Edit Modal -->
      <div id="editModal" class="modal">
        <div class="modal-content">
          <span class="close" onclick="closeEditModal()">×</span>
          <h2>تعديل الشهادة</h2>
          <form id="editForm" method="post" action="#" onsubmit="submitEditModal(); return false;">
            <input type="hidden" id="editCertificateId" name="editCertificateId">
            <div class="form-row">
              <!-- First Column -->
              <div class="form-container">
                <div class="form-group">
                  <label for="branchName">اسم الفرع</label>
                  <select id="branchName" required>
                    <option value="" disabled selected>اختر الفرع</option>        
                    <option value="محطه الرمل">محطه الرمل</option>
                    <option value="السلطان حسين">السلطان حسين</option>
                    <option value="الاستثماري">الاستثماري</option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="companyStatus">حالة المنشأه</label>
                  <select id="companyStatus" required>
                    <option value="مقيد">مقيد</option>
                    <option value="غير مقيد">غير مقيد</option>
                  </select>
                </div>    
                <div class="form-group">
                  <label for="office">اسم المكتب</label>
                  <select id="office" required>
                    <option value="">اسم المكتب</option>
                    <option value="عام">عام</option>
                    <option value="شرق">شرق</option>
                    <option value="برج العرب">برج العرب</option>
                    <option value="استثماري">استثماري</option>
                    <option value="غرفه">غرفه</option>                 
                  </select>
                </div>
                <div class="form-group">
                  <label for="registrationNumber">رقم السجل</label>
                  <input type="text" id="registrationNumber" placeholder="رقم السجل" required>
                </div>              
                <div class="form-group">
                  <label for="companyName">اسم الشركة</label>
                  <input type="text" id="companyName" required>
                </div>
                <div class="form-group">
                  <label for="companyAddress">عنوان الشركة</label>
                  <input type="text" id="companyAddress" required>
                </div>
                <div class="form-group">
                  <label for="companyType">نوع الشركة</label>
                  <select id="companyType" required>
                    <option value="" disabled selected>اختر نوع الشركه</option>
                    <option value="شركه">شركه</option>
                    <option value="فردي">فردي</option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="certificateNumber">رقم الشهادة</label>
                  <input type="text" id="certificateNumber" required>
                </div>
                <div class="form-group">
                  <label for="exportCountry">بلد التصدير</label>
                  <div class="search-select-container" style="width: 100%;">
                    <select id="exportCountry" class="search-select" required>
                      <option value=""></option>
                    </select>
                    <button type="button" class="add-button" onclick="openModal('exportCountry')">+</button>
                  </div>
                </div>
                <div class="form-group">
                  <label for="originCountry">بلد المنشأ</label>
                  <div class="search-select-container">
                    <select id="originCountry" class="search-select" required>
                      <option value="مصر" selected>مصر</option>
                    </select>
                    <button type="button" class="add-button" onclick="openModal('originCountry')">+</button>
                  </div>
                </div>  
              </div>
              <!-- Second Column -->
              <div class="form-container">
                <div class="form-group">
                  <label for="processDate">تاريخ العملية</label>
                  <input type="date" id="processDate" required>
                </div>
                <div class="form-group">
                  <label for="receiptNumber">رقم الايصال</label>
                  <input type="text" id="receiptNumber" required>
                </div>
                <div class="form-group">
                  <label for="receiptDate">تاريخ الايصال</label>
                  <input type="date" id="receiptDate" required>
                </div>
                <div class="form-group">
                  <label for="paymentAmount">القيمة المدفوعة</label>
                  <input type="number" id="paymentAmount" required>
                </div>
                <div class="form-group">
                  <label for="quantity_unit">نوع الوحدة (افتراضي الشهادة)</label>
                  <select id="quantity_unit" required>
                    <option value="" disabled selected>اختر نوع الوحدة</option>
                    <option value="طن">طن</option>
                    <option value="كجم">كجم</option>
                    <option value="وحده">وحده</option>    
                  </select>
                </div>
                <div class="form-group">
                  <label for="cost_currency">نوع العملة (افتراضي الشهادة)</label>
                  <select id="cost_currency" required>
                    <option value="" disabled selected>اختر نوع العملة</option>
                    <option value="USD">دولار</option>
                    <option value="EUR">يورو</option>
                    <option value="EGP">جنيه مصري</option>
                    <option value="GBP">الجنيه الإسترليني</option>
                    <option value="SAR">ريال سعودي</option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="importCompanyName">اسم الشركه المستورده</label>
                  <input type="text" id="importCompanyName" placeholder="ادخل اسم الشركه المستورده" required>
                </div>
                <div class="form-group">
                  <label for="importCompanyAddress">عنوان الشركه المستورده</label>
                  <input type="text" id="importCompanyAddress" placeholder="ادخل عنوان الشركه المستورده" required>
                </div>
                <div class="form-group">
                  <label for="importCompanyPhone">تليفون الشركه المستورده</label>
                  <input type="text" id="importCompanyPhone" placeholder="ادخل تليفون الشركه المستورده">
                </div>
                <!-- Shipments Section -->
                <div class="CenterContainer">
                  <div class="form-group">
                    <label for="shipment">الشحنه</label>
                    <button type="button" class="add-button" onclick="openModal('cargo')">اضف بضاعه جديده</button>
                    <button type="button" id="addShipmentGroup" class="add-button">اضف شحنه جديده</button>
                    <div id="shipmentContainer"></div>
                  </div>
                </div>
              </div>
            </div>
            {% if request.user.is_superuser or request.user.userprofile.is_branch_admin %}
            <button id="modalEditButton" type="submit">تعديل</button>          
            {% endif %}
          </form>
        </div>
      </div>

      <h1 class="centered-title">
بحث</h1>
      <div class="filter">
        <div class="form-group">
          <label for="filterOffice">اسم المكتب</label>
          <select id="filterOffice">
            <option value="">اختر مكتب</option>
            <option value="عام">عام</option>
            <option value="شرق">شرق</option>
            <option value="برج العرب">برج العرب</option>
            <option value="استثماري">استثماري</option>
            <option value="غرفه">غرفه</option>                
          </select>
        </div>
        <div class="form-group">
          <label for="filterRegistrationNumber">رقم السجل</label>
          <input type="text" id="filterRegistrationNumber" placeholder="ادخل رقم السجل">
        </div>
        <button id="searchButton" onclick="searchCertificates()">بحث</button>
      </div>
      
      <div class="table-container">
        <table id="resultsTable">
          <thead>
            <tr>
              <th>اسم المكتب</th>
              <th>رقم السجل</th>
              <th>رقم الشهادة</th>
              <th>اسم الشركة</th>
              <th>عنوان الشركة</th>
              <th>حالة المنشأه</th>
              <th>نوع الشركة</th>
              <th>اسم الفرع</th>
              <th>اسم الشركه المستورده</th>
              <th>عنوان الشركه المستورده</th>
              <th>تليفون الشركه المستورده</th>
              <th>البضاعة</th>
              <th>بلد التصدير</th>
              <th>بلد المنشأ</th>
              <th class="date-column">تاريخ العملية</th>
              <th>القيمة</th>
              <th>التكلفة</th>
              <th>رقم الايصال</th>
              <th class="date-column">تاريخ الايصال</th>
              <th>القيمة المدفوعة</th>
              <!-- Change the edit/delete button conditions -->
              {% if request.user.is_superuser or request.user.userprofile.is_branch_admin %}
              <th>تعديل</th>
              <th>حذف</th>
            {% endif %}            
            </tr>
          </thead>
          <tbody>
          </tbody>
        </table>
      </div>

      <!-- Modal for Adding New Items -->
      <div id="modal" class="modal">
        <div class="modal-content">
          <span class="close" onclick="closeModal()">×</span>
          <label for="newItem">إضافه عنصر جديد</label>
          <input type="text" id="newItem" placeholder="ادخل العنصر الجديد">
          <button type="button" onclick="addItem()">موافق</button>
        </div>
      </div>
    </div>
  </div>

  <div class="footer">
    <p>This website and system have been developed by the Alexandria Chamber of Commerce IT Department. For any inquiries, please contact the IT Department via <NAME_EMAIL>.
Unauthorized access, misuse, or any illegal activities related to this system are strictly prohibited and may result in legal action. All activities on this platform are monitored and logged for security purposes.</p>
      <img src="{% static 'ACOC.png' %}" alt="Footer Image" class="footer-img">
  </div>

  <!-- Shipment Template -->
<template id="shipmentTemplate">
  <div class="shipment-group">
    <div class="form-group inline-group">
      <div>
        <label>البضاعه</label>
        <div class="search-select-container">
          <select class="cargo search-select" required>
            <option value="" disabled selected>اختر البضاعة</option>
          </select>
        </div>
      </div>
      <div>
        <label>الكميه</label>
        <input type="number" step="0.01" class="quantity" placeholder="أدخل القيمة" required>
      </div>
      <div>
        <label>التكلفه</label>
        <input type="number" step="0.01" class="cost_amount" placeholder="أدخل التكلفة" required>
      </div>
      <button type="button" class="removeShipment">حذف الشحنة</button>
    </div>
  </div>
</template>

  {% if request.user.is_superuser or request.user.userprofile.is_branch_admin %}
  <script>window.isBranchAdmin = true;</script>
{% else %}
  <script>window.isBranchAdmin = false;</script>
{% endif %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/i18n/ar.min.js"></script>
<script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script>
  <script src="{% static 'script.js' %}"></script>
</body>
</html>