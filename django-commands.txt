# Django Commands to Create Admin Users
# Copy and paste these commands one by one in Django shell

# 1. Open Django shell
python manage.py shell

# 2. Import User model
from django.contrib.auth.models import User

# 3. Create admin user (Method 1)
User.objects.create_superuser('admin', '<EMAIL>', 'admin123')

# 4. Create super_admin user (Method 2)
User.objects.create_superuser('super_admin', '<EMAIL>', 'securepassword123')

# 5. Create root user (Method 3)
User.objects.create_superuser('root', '<EMAIL>', 'root123')

# 6. Check existing users
for user in User.objects.all():
    print(f"Username: {user.username}, Email: {user.email}, Is_superuser: {user.is_superuser}")

# 7. Reset password for existing user (if needed)
user = User.objects.get(username='admin')
user.set_password('newpassword123')
user.save()

# 8. Exit shell
exit()

# Alternative: Create user directly from command line
python manage.py createsuperuser --username admin --email <EMAIL>
