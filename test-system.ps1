# PowerShell script to test Origin Certificate System
# For Windows Server 2019

Write-Host "=== Testing Origin Certificate System ===" -ForegroundColor Green

# Function to test HTTP endpoint
function Test-Endpoint {
    param(
        [string]$Url,
        [string]$Name
    )
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✓ $Name - OK (Status: $($response.StatusCode))" -ForegroundColor Green
            return $true
        } else {
            Write-Host "✗ $Name - Failed (Status: $($response.StatusCode))" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "✗ $Name - Error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Check if Docker is running
Write-Host "Checking Docker status..." -ForegroundColor Yellow
try {
    $dockerVersion = docker version --format "{{.Server.Version}}"
    Write-Host "✓ Docker is running (Version: $dockerVersion)" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker is not running" -ForegroundColor Red
    exit 1
}

# Check if containers are running
Write-Host "`nChecking container status..." -ForegroundColor Yellow
$containers = docker-compose ps --services

foreach ($service in $containers) {
    $status = docker-compose ps $service | Select-String "Up"
    if ($status) {
        Write-Host "✓ $service container is running" -ForegroundColor Green
    } else {
        Write-Host "✗ $service container is not running" -ForegroundColor Red
    }
}

# Wait for services to be ready
Write-Host "`nWaiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Test web endpoints
Write-Host "`nTesting web endpoints..." -ForegroundColor Yellow
$webTests = @(
    @{ Url = "http://localhost:8000"; Name = "Main Application" },
    @{ Url = "http://localhost:8000/admin/"; Name = "Admin Panel" },
    @{ Url = "http://localhost:8000/login/"; Name = "Login Page" }
)

$passedTests = 0
$totalTests = $webTests.Count

foreach ($test in $webTests) {
    if (Test-Endpoint -Url $test.Url -Name $test.Name) {
        $passedTests++
    }
}

# Test database connection
Write-Host "`nTesting database connection..." -ForegroundColor Yellow
try {
    $dbTest = docker-compose exec -T web python -c "
import django
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OriginCertificateSystem.settings')
django.setup()
from django.db import connection
cursor = connection.cursor()
cursor.execute('SELECT 1')
print('Database connection successful')
"
    if ($dbTest -match "Database connection successful") {
        Write-Host "✓ Database connection - OK" -ForegroundColor Green
        $passedTests++
        $totalTests++
    } else {
        Write-Host "✗ Database connection - Failed" -ForegroundColor Red
        $totalTests++
    }
} catch {
    Write-Host "✗ Database connection - Error: $($_.Exception.Message)" -ForegroundColor Red
    $totalTests++
}

# Test static files
Write-Host "`nTesting static files..." -ForegroundColor Yellow
if (Test-Endpoint -Url "http://localhost:8000/static/styles.css" -Name "Static Files") {
    $passedTests++
}
$totalTests++

# Summary
Write-Host "`n=== Test Summary ===" -ForegroundColor Cyan
Write-Host "Passed: $passedTests/$totalTests tests" -ForegroundColor $(if ($passedTests -eq $totalTests) { "Green" } else { "Yellow" })

if ($passedTests -eq $totalTests) {
    Write-Host "🎉 All tests passed! System is working correctly." -ForegroundColor Green
} else {
    Write-Host "⚠️  Some tests failed. Check the logs for more details." -ForegroundColor Yellow
    Write-Host "Run 'docker-compose logs' to see detailed logs." -ForegroundColor White
}

# Show system information
Write-Host "`n=== System Information ===" -ForegroundColor Cyan
Write-Host "Application URL: http://localhost:8000" -ForegroundColor White
Write-Host "Admin Panel: http://localhost:8000/admin" -ForegroundColor White
Write-Host "Database: PostgreSQL on localhost:5432" -ForegroundColor White

# Show useful commands
Write-Host "`n=== Useful Commands ===" -ForegroundColor Cyan
Write-Host "View logs: docker-compose logs -f" -ForegroundColor White
Write-Host "Restart system: docker-compose restart" -ForegroundColor White
Write-Host "Stop system: docker-compose down" -ForegroundColor White
Write-Host "Create superuser: docker-compose exec web python manage.py createsuperuser" -ForegroundColor White
