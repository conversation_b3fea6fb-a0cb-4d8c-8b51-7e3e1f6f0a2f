<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
   <meta charset="UTF-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <title>تقرير الشهادات</title>
   {% load static humanize %}
   <link rel="stylesheet" href="{% static 'styles.css' %}">
   <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
   <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
   <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
   <style>
      input[type="date"],
      input[type="text"],
      select {
         width: 100%;
         padding: 8px;
         box-sizing: border-box;
         border: 1px solid #ccc;
         border-radius: 4px;
         font-size: 10px;
         background-color: #fff;
      }
      button {
         padding: 10px 20px;
         margin-top: 10px;
         cursor: pointer;
      }
      hr {
         margin: 20px 0;
      }
      .results-header {
         display: flex;
         justify-content: space-between;
         align-items: center;
         margin-bottom: 10px;
      }
      .table-container {
         overflow-x: auto;
         max-width: 100%;
      }
      table {
         width: 50%;
         border-collapse: collapse;
         background: white;
         direction: rtl;
         margin: 20px auto;
         direction: rtl;
      }
      table th,
      table td {
         border: 1px solid #dee2e6;
         padding: 6px;
         font-size: 12px;
         text-align: center;
      }
      table th {
         background-color: #3190d0;
         color: white;
         white-space: nowrap;
      }
      .date-column {
         white-space: nowrap;
      }
      .hide-in-pdf {
         display: table-cell;
      }
      @media print {
         .hide-in-pdf {
            display: none;
         }
      }
      .messages { color: red; text-align: center; }
      .cargo-selection {
         align-items: center;
         gap: 10px;
      }
      .selected-cargos {
         margin-top: 10px;
      }
      .selected-cargos ul {
         list-style: none;
         padding: 0;
      }
      .selected-cargos li {
         background: #e9ecef;
         padding: 5px 10px;
         margin: 5px 0;
         display: flex;
         justify-content: space-between;
         align-items: center;
      }
      .remove-cargo,
      .remove-country {
         cursor: pointer;
         color: red;
         font-weight: bold;
         margin-right: 5px;
      }
      .shipment-item {
         padding: 5px 0;
         border-bottom: 1px solid #dee2e6;
      }
      .shipment-item:last-child {
         border-bottom: none;
      }
      .suggestions {
         background: #fff;
         border: 1px solid #ccc;
         border-radius: 8px;
         max-height: 200px;
         overflow-y: auto;
         width: 100%;
         z-index: 1000;
         box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
         font-size: 14px;
         margin-top: 5px;
      }
      .suggestions div {
         padding: 10px 12px;
         cursor: pointer;
         border-bottom: 1px solid #f0f0f0;
         transition: background-color 0.2s ease;
      }
      .suggestions div:last-child {
         border-bottom: none;
      }
      .suggestions div:hover {
         background: #e6f0fa;
         color: #005566;
      }
      .suggestions::-webkit-scrollbar {
         width: 8px;
      }
      .suggestions::-webkit-scrollbar-track {
         background: #f1f1f1;
         border-radius: 8px;
      }
      .suggestions::-webkit-scrollbar-thumb {
         background: #888;
         border-radius: 8px;
      }
      .suggestions::-webkit-scrollbar-thumb:hover {
         background: #555;
      }
      .input-wrapper {
         position: relative;
         display: flex;
         align-items: center;
      }
   </style>
</head>
<body>
  <nav class="navbar">
    <div class="navbar-right" dir="ltr">
      <!-- <label id="headerRight2">نظام شهادة المصدر والمنشأ | </label> -->
      <label id="headerRight"> الغرفة التجارية المصرية بالاسكندرية </label>
      <img src="{% static 'ACOC_Logo_Final[1].png' %}" alt="Company Logo" class="logo">
    </div>
    <div class="navbar-left" dir="rtl">
      <label id="headerLeft"> Alexandria Chamber of Commerce </label>
      <!-- <label id="headerLeft2"> Origin Certificate System </label> -->
    </div>
    <button class="menu-btn" onclick="toggleSidebar()">☰</button>
<div class="user-dropdown">
  <button class="user-btn" onclick="toggleDropdown()">
    <span class="user-icon">👤</span>
    <span class="user-name">{{ request.user.get_full_name|default:request.user.username|truncatechars:12 }}</span>
  </button>
  <div class="dropdown-content" id="userDropdown">
    <div class="user-info">
      <p><strong>الاسم:</strong> {{ request.user.get_full_name|default:request.user.username }}</p>
      {% if request.user.userprofile.branch %}
        <p><strong>الفرع:</strong> {{ request.user.userprofile.branch.name }}</p>
      {% endif %}
      <p><strong>الدور:</strong> 
        {% if request.user.is_superuser %}
          المشرف العام
        {% elif request.user.userprofile.is_branch_admin %}
          مدير الفرع
        {% else %}
          مستخدم
        {% endif %}
      </p>
    </div>
    <form action="{% url 'logout' %}" method="post" style="display: inline;">
      {% csrf_token %}
      <button type="submit" class="logout-btn">
        <span>تسجيل الخروج</span>
        <i class="fas fa-sign-out-alt"></i>
      </button>
    </form>
  </div>
</div>
</nav>

  <!-- Sidebar -->
<div class="sidebar" id="sidebar">
    <a href="javascript:void(0)" onclick="toggleSidebar()">✖</a>
    <a href="{% url 'index' %}"><i class="fas fa-home"></i> الصفحة الرئيسية</a>
    <a href="{% url 'filter' %}"><i class="fas fa-search"></i> بحث</a>
    <a href="{% url 'cargo' %}"><i class="fas fa-boxes"></i> البضائع</a>
    <a href="{% url 'country' %}"><i class="fas fa-globe"></i> البلد</a>
    <a href="{% url 'report' %}"><i class="fas fa-chart-bar"></i> التقرير</a>
</div>

   <div class="content-wrapper">
      <div class="container">
         <h1 class="centered-title">
تقرير الشهادات</h1>
        <form method="get" action="{% url 'report' %}" id="reportForm">
    <div class="form-group">
        <label for="branchName">اسم الفرع:</label>
       <select id="branchName" name="branch_name" {% if not request.user.is_superuser %}disabled{% endif %}>
    <option value="" {% if not branch_name %}selected{% endif %}>اختر الفرع</option>
    <option value="محطه الرمل" {% if branch_name == "محطه الرمل" or request.user.userprofile.branch.name == "محطه الرمل" %}selected{% endif %}>محطه الرمل</option>
    <option value="السلطان حسين" {% if branch_name == "السلطان حسين" or request.user.userprofile.branch.name == "السلطان حسين" %}selected{% endif %}>السلطان حسين</option>
    <option value="الاستثماري" {% if branch_name == "الاستثماري" or request.user.userprofile.branch.name == "الاستثماري" %}selected{% endif %}>الاستثماري</option>
</select>
    </div>
    <div class="form-group">
        <label for="process_date_from">تاريخ العملية من:</label>
        <input type="date" id="process_date_from" name="process_date_from" value="{{ process_date_from }}">
    </div>
    <div class="form-group">
        <label for="process_date_to">تاريخ العملية إلى:</label>
        <input type="date" id="process_date_to" name="process_date_to" value="{{ process_date_to }}">
    </div>
    <div class="form-group">
        <label for="export_country">بلد التصدير:</label>
        <div class="input-wrapper">
            <input type="text" id="countrySearch" placeholder="ابحث عن بلد..." 
                   onkeyup="searchCountries(this.value)" onfocus="searchCountries('')" 
                   value="{{ selected_country }}" autocomplete="off">
            <span class="remove-country" onclick="clearCountry()" style="display: none;">×</span>
        </div>
        <input type="hidden" name="export_country" id="export_country_hidden" value="{{ selected_country }}">
        <div id="countrySuggestions" class="suggestions" style="display: none;"></div>
    </div>
    <div class="form-group">
        <label for="cargo">البضاعة:</label>
        <div class="cargo-selection">
            <div class="input-wrapper">
                <input type="text" id="cargoSearch" placeholder="ابحث عن بضاعة..." 
                       onkeyup="debouncedSearchCargos(this.value)" onfocus="searchCargos('')" autocomplete="off">
            </div>
            <div id="cargoSuggestions" class="suggestions" style="display: none;"></div>
            <button type="button" onclick="addCargoFromSearch()">إضافة</button>
        </div>
        <div class="selected-cargos">
            <ul id="selectedCargosList">
                {% for cargo in selected_cargos %}
                <li data-id="{{ cargo.id }}">{{ cargo.ExportedGoods }} <span class="remove-cargo" onclick="removeCargo(this)">×</span></li>
                {% endfor %}
            </ul>
            <input type="hidden" name="cargo_ids" id="cargoIdsInput" value="{{ selected_cargo_ids|join:',' }}">
        </div>
    </div>
    <input type="hidden" name="submitted" value="true">
    <button type="submit">موافق</button>
</form>

         <hr>
         {% if messages %}
            <div class="messages">
               {% for message in messages %}
                  <p>{{ message }}</p>
               {% endfor %}
            </div>
         {% endif %}
         <div class="results-header">
            <h2>نتائج التقرير</h2>
            <p id="certificateCount" style="margin: 0; font-weight: bold;"></p>
         </div>
        <div class="table-container">
    {% if certificates %}
    <table id="certificatesTable">
        <thead>
            <tr>
                <th class="hide-in-pdf">المعرف</th>
                <th>اسم المكتب</th>
                <th>رقم السجل</th>
                <th>رقم الشهادة</th>
                <th>اسم الشركة</th>
                <th>عنوان الشركة</th>
                <th class="hide-in-pdf">حالة المنشأه</th>
                <th class="hide-in-pdf">نوع الشركة</th>
                <th>اسم الفرع</th>
                <th>اسم الشركه المستورده</th>
                <th>عنوان الشركه المستورده</th>
                <th>تليفون الشركه المستورده</th>
                <th>البضائع</th>
                <th>بلد التصدير</th>
                <th>بلد المنشأ</th>
                <th class="date-column">تاريخ العملية</th>
                <th class="hide-in-pdf">رقم الايصال</th>
                <th class="hide-in-pdf date-column">تاريخ الايصال</th>
                <th class="hide-in-pdf">القيمة المدفوعة</th>
                <th>القيمة (الكمية)</th>
                <th>التكلفة</th>
            </tr>
        </thead>
        <tbody>
            {% for cert in certificates %}
            <tr class="data-row">
                <td class="hide-in-pdf">{{ cert.id }}</td>
                <td>{{ cert.Office.OfficeName|default:'' }}</td>
                <td>{{ cert.RegistrationNumber|default:'' }}</td>
                <td>{{ cert.CertificateNumber|default:'' }}</td>
                <td>{{ cert.Company.CompanyName|default:'' }}</td>
                <td>{{ cert.Company.CompanyAddress|default:'' }}</td>
                <td class="hide-in-pdf">{{ cert.Company.CompanyStatus|default:'' }}</td>
                <td class="hide-in-pdf">{{ cert.Company.CompanyType|default:'' }}</td>
                <td>{{ cert.BranchName|default:'' }}</td>
                <td>{{ cert.importCompanyName|default:'' }}</td>
                <td>{{ cert.importCompanyAddress|default:'' }}</td>
                <td>{{ cert.importCompanyPhone|default:'' }}</td>
                <td>
                    {% for shipment in cert.shipments.all %}
                    <div class="shipment-item">
                        {{ shipment.cargo.ExportedGoods }}{% if shipment.quantity and cert.quantity_unit %}, {{ shipment.quantity }} {{ cert.quantity_unit }}{% endif %}{% if shipment.cost_amount and cert.default_currency %}, {{ shipment.cost_amount|intcomma }} {{ cert.default_currency }}{% endif %}
                    </div>
                    {% empty %}
                    غير متوفر
                    {% endfor %}
                </td>
                <td>{{ cert.ExportCountry.CountryName|default:'' }}</td>
                <td>{{ cert.OriginCountry.CountryName|default:'' }}</td>
                <td class="date-column">{{ cert.IssueDate|default:'' }}</td>
                <td class="hide-in-pdf">{{ cert.ReceiptNumber|default:'' }}</td>
                <td class="hide-in-pdf date-column">{{ cert.ReceiptDate|default:'' }}</td>
                <td class="hide-in-pdf">{{ cert.PaymentAmount|floatformat:2|intcomma|default:'' }}</td>
                <td>{{ cert.total_quantity|floatformat:2|intcomma|default:'' }} {{ cert.quantity_unit|default:'' }}</td>
                <td>{{ cert.total_cost.amount|floatformat:2|intcomma|default:'' }} {{ cert.default_currency|default:'' }}</td>
            </tr>
            {% empty %}
            <tr class="no-data">
                <td colspan="21">لا توجد بيانات</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p>اضغط على "موافق" لعرض البيانات</p>
    {% endif %}
</div>
         <!-- Totals table rendered outside the main table -->
         <div id="totalsContainer">
            <table class="totals-table">
               <tr>
                  {% for total in currency_totals %}
                  <th>{{ total.cost_currency }}</th>
                  {% endfor %}
               </tr>
               <tr>
                  {% for total in currency_totals %}
                  <td>{{ total.total_cost|floatformat:2|intcomma }}</td>
                  {% endfor %}
               </tr>
            </table>
         </div>
         <div class="download-buttons">
            <form id="exportForm" method="get" action="{% url 'report_download' 'excel' %}" style="display: inline;">
               <label>اختر الأعمدة للتصدير:</label><br>
               <input type="checkbox" name="columns" value="id" checked> المعرف
               <input type="checkbox" name="columns" value="cargo" checked> البضائع
               <input type="checkbox" name="columns" value="cost" checked> التكلفة
               <input type="checkbox" name="columns" value="export_country" checked> بلد التصدير
               <input type="checkbox" name="columns" value="branch" checked> اسم الفرع
               <input type="checkbox" name="columns" value="issue_date" checked> تاريخ العملية
               <input type="hidden" name="process_date_from" value="{{ process_date_from }}">
               <input type="hidden" name="process_date_to" value="{{ process_date_to }}">
               <input type="hidden" name="export_country" value="{{ selected_country }}">
               <input type="hidden" name="branch_name" value="{{ branch_name }}">
               <input type="hidden" name="cargo_ids" value="{{ selected_cargo_ids|join:',' }}">
               <button type="submit">البيانات المحدده Excel</button>
            </form>
            <button>
               <a href="{% url 'report_download' 'excel' %}?process_date_from={{ process_date_from }}&process_date_to={{ process_date_to }}&export_country={{ selected_country }}{% for cargo_id in selected_cargo_ids %}&cargo_ids={{ cargo_id }}{% endfor %}">
               Excel
               </a>
            </button>
            <button onclick="printTableAsPDF()">PDF</button>
            <button>
               <a href="{% url 'empty_report_download' 'excel' %}">النموذج</a>
            </button>
         </div>
      </div>
   </div>

   <div class="footer">
    <p>This website and system have been developed by the Alexandria Chamber of Commerce IT Department. For any inquiries, please contact the IT Department via <NAME_EMAIL>.
Unauthorized access, misuse, or any illegal activities related to this system are strictly prohibited and may result in legal action. All activities on this platform are monitored and logged for security purposes.</p>
      <img src="{% static 'ACOC.png' %}" alt="Footer Image" class="footer-img">
   </div>

   <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
   <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
   <script>
      $(document).ready(function() {
         $('#certificatesTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "lengthMenu": [200, 400,600,800,1000],
            "language": {
               "search": "بحث:",
               "lengthMenu": "عرض _MENU_ سجلات لكل صفحة",
               "zeroRecords": "لا توجد بيانات مطابقة",
               "info": "عرض _START_ إلى _END_ من _TOTAL_ سجل",
               "infoEmpty": "لا توجد سجلات متاحة",
               "infoFiltered": "(تمت تصفيتها من _MAX_ سجل إجمالي)",
               "paginate": {
                  "first": "الأول",
                  "last": "الأخير",
                  "next": "التالي",
                  "previous": "السابق"
               }
            }
         });
         const countrySearch = document.getElementById('countrySearch');
         if (countrySearch.value) {
            document.querySelector('.remove-country').style.display = 'inline';
         }
      });

      function updateCertificateCount() {
         const rows = document.querySelectorAll("#certificatesTable tbody tr.data-row");
         const count = rows.length;
         document.getElementById("certificateCount").textContent = "عدد الشهادات: " + count;
      }
      document.addEventListener("DOMContentLoaded", updateCertificateCount);

      window.onload = function() {
         window.history.replaceState({}, document.title, window.location.pathname);
      };

      function toggleSidebar() {
         var sidebar = document.getElementById("sidebar");
         sidebar.classList.toggle("active");
      }

      // Debounce function to reduce search requests
      function debounce(func, wait) {
         let timeout;
         return function executedFunction(...args) {
            const later = () => {
               clearTimeout(timeout);
               func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
         };
      }

      document.addEventListener('click', function(e) {
         const sidebar = document.getElementById('sidebar');
         const menuBtn = document.querySelector('.menu-btn');
         const countrySuggestions = document.getElementById('countrySuggestions');
         const cargoSuggestions = document.getElementById('cargoSuggestions');
         const countrySearch = document.getElementById('countrySearch');
         const cargoSearch = document.getElementById('cargoSearch');

         if (sidebar.classList.contains('active') && !sidebar.contains(e.target) && !menuBtn.contains(e.target)) {
            sidebar.classList.remove('active');
         }

         if (!countrySuggestions.contains(e.target) && e.target !== countrySearch && document.activeElement !== countrySearch) {
            countrySuggestions.style.display = 'none';
         }

         if (!cargoSuggestions.contains(e.target) && e.target !== cargoSearch && document.activeElement !== cargoSearch) {
            cargoSuggestions.style.display = 'none';
         }
      });

      let selectedCountry = '{{ selected_country|default:'' }}';
      function searchCountries(query) {
         $.ajax({
            url: '{% url "search_countries" %}',
            data: { 'q': query },
            success: function(data) {
               const suggestions = document.getElementById('countrySuggestions');
               suggestions.innerHTML = '';
               if (data.length > 0) {
                  data.forEach(country => {
                     const div = document.createElement('div');
                     div.textContent = country.CountryName;
                     div.onclick = function() {
                        selectedCountry = country.CountryName;
                        document.getElementById('countrySearch').value = country.CountryName;
                        document.getElementById('export_country_hidden').value = country.CountryName;
                        suggestions.style.display = 'none';
                        document.querySelector('.remove-country').style.display = 'inline';
                     };
                     suggestions.appendChild(div);
                  });
                  suggestions.style.display = 'block';
               } else {
                  suggestions.innerHTML = '<div>لا توجد نتائج</div>';
                  suggestions.style.display = 'block';
               }
            }
         });
      }

      function clearCountry() {
         document.getElementById('countrySearch').value = '';
         document.getElementById('export_country_hidden').value = '';
         document.querySelector('.remove-country').style.display = 'none';
         selectedCountry = null;
      }

      let selectedCargo = null;
      function searchCargos(query) {
         $.ajax({
            url: '{% url "search_cargos" %}',
            data: { 'q': query },
            success: function(data) {
               const suggestions = document.getElementById('cargoSuggestions');
               suggestions.innerHTML = '';
               if (data.length > 0) {
                  data.forEach(cargo => {
                     const div = document.createElement('div');
                     div.textContent = cargo.ExportedGoods;
                     div.setAttribute('data-id', cargo.id);
                     div.onclick = function() {
                        selectedCargo = { id: cargo.id, name: cargo.ExportedGoods };
                        document.getElementById('cargoSearch').value = cargo.ExportedGoods;
                        suggestions.style.display = 'none';
                     };
                     suggestions.appendChild(div);
                  });
                  suggestions.style.display = 'block';
               } else {
                  suggestions.innerHTML = '<div>لا توجد نتائج</div>';
                  suggestions.style.display = 'block';
               }
            }
         });
      }

      const debouncedSearchCargos = debounce(searchCargos, 300);

      function addCargoFromSearch() {
         if (!selectedCargo) return;
         const selectedList = document.getElementById('selectedCargosList');
         const existingIds = Array.from(selectedList.getElementsByTagName('li')).map(li => li.getAttribute('data-id'));
         if (!existingIds.includes(selectedCargo.id.toString())) {
            const li = document.createElement('li');
            li.setAttribute('data-id', selectedCargo.id);
            li.innerHTML = `${selectedCargo.name} <span class="remove-cargo" onclick="removeCargo(this)">×</span>`;
            selectedList.appendChild(li);
            updateCargoIdsInput();
         }
         document.getElementById('cargoSearch').value = '';
         selectedCargo = null;
      }

      function removeCargo(element) {
         element.parentElement.remove();
         updateCargoIdsInput();
      }

      function updateCargoIdsInput() {
    const selectedList = document.getElementById('selectedCargosList');
    const cargoIds = Array.from(selectedList.getElementsByTagName('li'))
        .map(li => li.getAttribute('data-id'))
        .filter(id => id);  // Remove empty/null IDs
    document.getElementById('cargoIdsInput').value = cargoIds.join(',');
    document.getElementById('exportCargoIds').value = cargoIds.join(','); // Sync with export form
}

      function printTableAsPDF() {
         let printWindow = window.open('', '_blank');
         if (!printWindow) {
             alert('يرجى السماح بفتح النوافذ المنبثقة');
             return;
         }
         let table = document.getElementById('certificatesTable');
         let countElement = document.getElementById('certificateCount');
         let certCountText = countElement ? countElement.textContent : '';
         if (!table) {
             alert('الجدول غير موجود!');
             printWindow.close();
             return;
         }

         // Clone the main table
         let clonedTable = table.cloneNode(true);
         // Remove unnecessary columns (indexes: 0, 6, 7, 16, 17, 18)
         const columnsToRemove = [0, 6, 7, 16, 17, 18];
         let thead = clonedTable.querySelector('thead tr');
         if (thead) {
            Array.from(thead.children).forEach((th, index) => {
               if (columnsToRemove.includes(index)) {
                  th.remove();
               }
            });
         }
         let tbodyRows = clonedTable.querySelectorAll('tbody tr');
         if (tbodyRows.length) {
            tbodyRows.forEach(row => {
               Array.from(row.children).forEach((td, index) => {
                  if (columnsToRemove.includes(index)) {
                     td.remove();
                  }
               });
            });
         }
         // Get totals table from the separate container
         let totalsContainer = document.getElementById('totalsContainer');
         let totalsHTML = totalsContainer ? totalsContainer.innerHTML : '';
         let htmlContent = `
         <html lang="ar" dir="rtl">
            <head>
               <meta charset="UTF-8">
               <meta name="viewport" content="width=device-width, initial-scale=1.0">
               <title>طباعة الشهادات</title>
               <style>
                  @page { size: A4 landscape; margin: 20mm; }
                  body { font-family: Arial, sans-serif; margin: 0; padding: 10mm; background-color: #f4f6f9; text-align: center; }
                  .container { max-width: 1200px; margin: auto; background: white; padding: 15px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); }
                  .header { display: flex; justify-content: space-between; align-items: center; padding: 10px 5px; }
                  .header-item { display: flex; align-items: center; gap: 5px; font-size: 18px; font-weight: bold; color: #1866ae; }
                  .header img { height: 45px; }
                  .header-title { font-size: 24px; font-weight: bold; color: #1866ae; text-align: center; margin-top: 10px; }
                  table { width: 50%; margin: 20px auto; border-collapse: collapse; background: white; direction: rtl; }
                  table th, table td { border: 1px solid #dee2e6; padding: 6px; font-size: 10px; text-align: center; }
                  table th { background-color: #2d78ab; color: white; white-space: nowrap; }
                  .totals-table { width: 50%; margin: 10px auto; border-collapse: collapse; }
                  .totals-table th, .totals-table td { border: 1px solid #dee2e6; padding: 8px 12px; font-size: 14px; text-align: center; }
                  .totals-table th { background-color: #3190d0; color: white; }
               </style>
            </head>
            <body>
               <div class="header">
                  <div class="header-item">
                     <img src="/static/ACOC.png" alt="شعار الغرفة التجارية">
                     <span>الغرفة التجارية المصرية بالإسكندرية</span>
                  </div>
                  <div class="header-item">
                     <span>Alexandria Chamber of Commerce</span>
                     <img src="/static/ACOC_Logo_Final[1].png" alt="Alexandria Chamber of Commerce Logo">
                  </div>
               </div>
               <div class="header-title">شهادة المصدر</div>
               <div class="container">
                  <div class="count">${certCountText}</div>
                  ${clonedTable.outerHTML}
                  <div id="printTotals">${totalsHTML}</div>
               </div>
               <script>
                  window.onload = function() {
                     window.print();
                     window.close();
                  }
               <\/script>
            </body>
         </html>
         `;
         printWindow.document.open();
         printWindow.document.write(htmlContent);
         printWindow.document.close();
      }
      function toggleDropdown() {
    document.getElementById("userDropdown").classList.toggle("show-dropdown");
}

// Close the dropdown if clicked outside
window.onclick = function(event) {
    if (!event.target.matches('.user-btn') && !event.target.closest('.user-dropdown')) {
        const dropdowns = document.getElementsByClassName("dropdown-content");
        for (let i = 0; i < dropdowns.length; i++) {
            const openDropdown = dropdowns[i];
            if (openDropdown.classList.contains('show-dropdown')) {
                openDropdown.classList.remove('show-dropdown');
            }
        }
    }
}
   </script>
</body>
</html>